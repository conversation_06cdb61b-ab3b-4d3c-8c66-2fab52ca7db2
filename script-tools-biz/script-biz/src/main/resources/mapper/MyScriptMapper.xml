<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ideal.script.mapper.MyScriptMapper">

    <resultMap id="selectMyScriptListMap" type="com.ideal.script.model.bean.MyScriptBean">
        <result column="iscript_info_id" property="scriptInfoId" />
        <result column="iunique_uuid" property="uniqueUuid"/>
        <result column="iscript_name_zh" property="scriptNameZh"/>
        <result column="iscript_name" property="scriptName"/>
        <result column="iplatform" property="platform"/>
        <result column="iexecuser" property="execuser"/>
        <result column="iedit_state" property="editState"/>
        <result column="iversion" property="version"/>
        <result column="icategory_id" property="categoryId"/>
        <result column="ilevel" property="level"/>
        <result column="isrc_script_uuid" property="srcScriptUuid"/>
        <result column="iscript_source" property="scriptSource"/>
        <result column="iscript_label" property="scriptLabel"/>
        <result column="icreator_name" property="creatorName" />
        <result column="icategory_path" property="categoryPath"/>
        <result column="iscript_type" property="scriptType"/>
        <result column="iscript_info_version_id" property="scriptInfoVersionId"/>
        <result column="iexpect_lastLine" property="expectLastline"/>
        <result column="iexpect_type" property="expectType"/>
        <result column="is_default" property="isDefault"/>
        <result column="iconfirm_state" property="confirmState"/>
        <result column="iupgrade_type" property="upgradeType"/>
        <result column="iuse_state" property="useState"/>
    </resultMap>

    <resultMap id="itsmChildrenMap" type="com.ideal.script.model.bean.ItsmChildrenBean">
        <result column="iid" property="iid" />
        <result column="icategory_path" property="scriptCategory"/>
        <result column="iscript_name_zh" property="scriptNameZh"/>
        <result column="iscript_name" property="scriptName"/>
        <result column="iplatform" property="platform"/>
        <result column="iexecuser" property="execuser"/>
    </resultMap>

    <resultMap id="ScriptVersionResultMap" type="com.ideal.script.model.bean.ScriptVersionInfoBean">
        <result column="iid" property="id" />
        <result column="icreate_time" property="createTime" />
        <result column="iupdate_time" property="updateTime" />
        <result column="iscript_name_zh" property="scriptNameZh" />
        <result column="icategory_id" property="categoryId" />
        <result column="icreator_name" property="creatorName" />
        <result column="iupdator_name" property="updatorName" />
        <result column="iversion" property="version" />
        <result column="isrc_script_uuid" property="srcScriptUuid" />
        <result column="iinfo_unique_uuid" property="infoUniqueUuid" />
        <result column="iparent_id" property="parentId" />
        <result column="icontent" property="content"/>
        <result column="iuse_state" property="useState"/>
        <result column="iversionid" property="versionId"/>
        <result column="is_default" property="isDefault"/>
        <result column="iscript_source" property="scriptSource"/>
        <result column="iaudit_user" property="auditUser"/>
        <result column="iaudit_time" property="auditTime"/>
    </resultMap>

    <resultMap id="selectContentInfo" type="com.ideal.script.model.entity.InfoVersionText">
        <result property="content" column="icontent"/>
    </resultMap>

    <resultMap id="selectInfoListMap" type="com.ideal.script.model.bean.DownloadScriptBean">
        <result property="scriptName" column="iscript_name"/>
        <result property="scriptType" column="iscript_type"/>
        <result property="uniqueUuid" column="iunique_uuid"/>
        <result property="srcScriptUuid" column="isrc_script_uuid"/>
        <result property="content" column="icontent"/>
    </resultMap>


    <resultMap id="ScriptsWithoutVersionsMap" type="com.ideal.script.model.entity.InfoVersion">
        <result property="id" column="iid"/>
        <result property="srcScriptUuid" column="isrc_script_uuid"/>
    </resultMap>

    <select id="selectMyScriptList" resultMap="selectMyScriptListMap">
        SELECT
        a.iid as iscript_info_id,
        a.iunique_uuid,
        a.iscript_name_zh,
        a.iscript_name,
        a.iplatform,
        a.iexecuser,
        a.icreator_name,
        a.icategory_id,
        a.iscript_source,
        a.iscript_label,
        a.iedit_state,
        a.isys_org_code,
        a.iupdate_time,
        a.icategory_path,
        a.ideleted,
        p.icodevalue,
        a.iscript_type
        <if test="dubboFlag != null and dubboFlag">
            ,b.iversion,
            b.isrc_script_uuid,
            b.iid as iscript_info_version_id,
            b.iexpect_type,
            b.iexpect_lastline,
            b.is_default,
            b.iupgrade_type,
            b.iuse_state
        </if>
        FROM
        (SELECT DISTINCT
        ai.iid, ai.iunique_uuid, ai.iscript_name_zh, ai.iscript_name,
        ai.iplatform, ai.iexecuser, ai.icreator_name, ai.icategory_id,
        ai.iscript_source, ai.iscript_label, ai.iedit_state,
        ai.isys_org_code, ai.iupdate_time, ai.icategory_path,
        ai.ideleted, ai.iscript_type
        FROM
        ieai_script_info ai
        LEFT JOIN ieai_script_system_relation s ON ai.iunique_uuid = s.iinfo_unique_uuid
        WHERE
        ai.ideleted = 0
        <!--只有用分类作为查询条件时才会使用到这个条件-->
        <if test="categoryPath != null and categoryPath != '' and escapedLikeCategoryPath != null and escapedLikeCategoryPath != ''">
            <bind name="categoryPath" value="categoryPath +'%'"/>
            AND (ai.icategory_path like #{categoryPath}
            <bind name="escapedLikeCategoryPath" value="escapedLikeCategoryPath + '/' +'%'"/>
            OR ai.icategory_path like #{escapedLikeCategoryPath})
        </if>
        <!-- permissionType == 2 根据用户查询-->
        <if test="permissionType == 2">
            <!-- 不是管理员时拼接管用户查询条件-->
            <if test="!superUser">
                <if test="creatorName != null and creatorName != ''">
                    AND ai.icreator_name = #{creatorName}
                </if>
            </if>
        </if>
        <!-- permissionType == 4 根据角色查询，先查出复核的分类，并排除非绑定的角色对应的用户-->
        <if test="permissionType == 4">
            <!-- 不是管理员时拼接管用户查询条件-->
            <if test="!superUser">
                <if test="excludeUserIds != null and excludeUserIds.size != 0 ">
                    and ( ai.icreator_id in
                    <foreach collection="excludeUserIds" item="excludeUserId" open="(" separator="," close=")">
                        #{excludeUserId}
                    </foreach>
                    <if test="orgCategoryPath != null and orgCategoryPath.size != 0">
                        or
                        <foreach collection="orgCategoryPath" item="path" separator="OR">
                            ai.icategory_path like #{path}
                        </foreach>
                    </if>
                    )
                </if>
            </if>
        </if>
        <!-- permissionType == 3 根据部门查询-->
        <if test="permissionType == 3">
            <if test="sysOrgCode != null and sysOrgCode != '' and !superUser">
                <bind name="sysOrgCode" value="sysOrgCode + '%'"/>
                AND (ai.isys_org_code like #{sysOrgCode}
                <if test="orgCategoryPath != null and orgCategoryPath.size != 0">
                    OR
                    <foreach collection="orgCategoryPath" item="path" separator="OR">
                        ai.icategory_path like #{path}
                    </foreach>
                </if>
                )
            </if>
        </if>
        <!--如果分类路径集合不为空，那么将条件拼接上去，通过传递过来的字段适用in或者not in-->
        <if test="categroyPathSearchList != null and categroyPathSearchList.size > 0">
            <!--true时使用not in-->
            <if test="categoryPathNotInFlag">
                and ai.icategory_path not in
            </if>
            <!--false时使用in-->
            <if test="!categoryPathNotInFlag">
                and ai.icategory_path in
            </if>
            <foreach collection="categroyPathSearchList" item="categroyPathStr" open="(" separator="," close=")">
                #{categroyPathStr}
            </foreach>
        </if>

        ) a
        LEFT JOIN ieai_platform_code p ON p.iname = a.iplatform
        <if test="dubboFlag != null and dubboFlag">
            JOIN ieai_script_info_version b ON a.iunique_uuid = b.iinfo_unique_uuid
            AND b.ideleted = 0
           /*showDisableScript为true的时候启用、禁用的脚本都需要查出来，否则只查询启用的脚本*/
            <if test="showDisableScript == null or !showDisableScript">
                AND b.iuse_state = 1
            </if>
            <if test="scriptLevel != null">
                AND b.ilevel = #{scriptLevel}
            </if>
            <!--查询所有版本标识为true，查询所有版本-->
            <if test="allVersions != null and allVersions">
                <if test="srcScriptUuid != null and srcScriptUuid != ''">
                    AND b.iinfo_unique_uuid = (select iinfo_unique_uuid from ieai_script_info_version where isrc_script_uuid = #{srcScriptUuid})
                </if>
            </if>
            <!--查询所有版本标识不为true，查询特定版本-->
            <if test="allVersions == null or !allVersions">
                <if test="srcScriptUuid != null and srcScriptUuid != ''">
                    AND b.isrc_script_uuid = #{srcScriptUuid}
                </if>
            </if>
            <if test="srcScriptUuids != null and srcScriptUuids.size > 0">
                AND b.isrc_script_uuid in
                <foreach collection="srcScriptUuids" item="srcScriptUuidStr" open="(" separator="," close=")">
                    #{srcScriptUuidStr}
                </foreach>
            </if>
            <if test="scriptInfoVersionId != null">
                AND b.iid = #{scriptInfoVersionId}
            </if>
            <if test="showDefaultVersion != null and showDefaultVersion">
                AND b.is_default = 1
            </if>
            <if test="draftFlag != null and draftFlag and allVersions != null and !allVersions">
                AND (
                (EXISTS (SELECT 1 FROM ieai_script_info_version v WHERE v.iinfo_unique_uuid = a.iunique_uuid AND v.iversion IS NULL) AND b.iedit_state != 1)
                OR
                (NOT EXISTS (SELECT 1 FROM ieai_script_info_version v WHERE v.iinfo_unique_uuid = a.iunique_uuid AND v.iversion IS NULL) AND b.is_default = 1)
                )
            </if>
            <if test="(allVersions != null or allVersions) and excludeDefault != null and excludeDefault">
                AND b.is_default = 0
            </if>
            <if test="excludeVersionIdList != null and excludeVersionIdList.size() > 0">
                AND b.iid not in
                <foreach collection="excludeVersionIdList" item="scriptInfoVersionId" open="(" separator="," close=")">
                    #{scriptInfoVersionId}
                </foreach>
            </if>
            <if test="draftFlag != null and !draftFlag">
                AND b.iedit_state = 1
            </if>
            <if test="infoUniqueUuid != null and infoUniqueUuid != ''">
                AND b.iinfo_unique_uuid = #{infoUniqueUuid}
            </if>
        </if>
        <where>
            <trim suffixOverrides="and">
                <if test="keyword != null">
                    <bind name="keyword" value="'%' + keyword.toUpperCase() + '%'"/>
                    AND (
                    UPPER(a.iscript_name_zh) like UPPER(#{keyword})
                    OR UPPER(a.iscript_name) like UPPER(#{keyword})
                    OR EXISTS (
                    SELECT 1
                    FROM ieai_script_info_version b1
                    JOIN ieai_script_info_version_text t1 ON b1.isrc_script_uuid = t1.isrc_script_uuid
                    WHERE b1.iinfo_unique_uuid = a.iunique_uuid
                    AND b1.iedit_state = a.iedit_state
                    AND UPPER(t1.icontent) LIKE UPPER(#{keyword})
                    )
                    )
                </if>
                <if test="creatorName != null and creatorName != ''">
                    AND a.icreator_name like concat('%', trim(#{creatorName}), '%')
                </if>
                <if test="scriptNameZh != null and scriptNameZh != ''">
                    AND a.iscript_name_zh like concat('%', trim(#{scriptNameZh}), '%')
                </if>
                <if test="scriptName != null and scriptName != ''">
                    AND a.iscript_name like concat('%', #{scriptName}, '%')
                </if>
                <if test="editState != null">
                    AND a.iedit_state = #{editState}
                </if>
                <if test="platform != null and platform != ''">
                    <bind name="platformLike" value="'%'+platform+'%'"/>
                    AND a.iplatform like #{platformLike}
                </if>
                <if test="scriptLabel != null and scriptLabel != ''">
                    AND a.iscript_label like concat('%', #{scriptLabel}, '%')
                </if>
                <if test="scriptSource != null">
                    AND a.iscript_source = #{scriptSource}
                </if>
                <if test="scriptType != null and scriptType != ''">
                    AND a.iscript_type = #{scriptType}
                </if>
            </trim>
        </where>
        ORDER BY
        a.iupdate_time desc
        <if test="dubboFlag != null and dubboFlag">
            ,b.iversion desc
        </if>
    </select>

     <select id="getLastVersion" parameterType="String" resultType="String">
         select iversion
         from ieai_script_info_version
         where iedit_state = 1
         and iinfo_unique_uuid = (select iinfo_unique_uuid
         from ieai_script_info_version
         where isrc_script_uuid = #{versionUuid})
     </select>

    <select id="getScriptListForDownload" resultMap="selectInfoListMap">
        select A.*
        from (select i.IID,
        iunique_uuid,
        iscript_name_zh,
        iscript_type,
        iscript_name,
        i.icreate_time,
        i.icreator_id,
        i.iedit_state,
        iwhite_command,
        IPLATFORM,
        v.ILEVEL,
        icontent,
        v.isrc_script_uuid,
        1 as IFROM
        from IEAI_SCRIPT_INFO i
        left join ieai_script_info_version v on i.iunique_uuid = v.iinfo_unique_uuid
        left join ieai_script_info_version_text t on v.isrc_script_uuid = t.isrc_script_uuid
        where 1 = 1
        AND v.iid in
        <foreach collection="iid" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
           ) A
    </select>

    <select id="getWorkitemIdByVersionIds" resultType="java.lang.Long">
        select iappr_workitem_id from ieai_script_audit_relation where isrc_script_uuid in (select isrc_script_uuid from ieai_script_info_version where iid in
        <foreach collection="scriptVersionIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        )
    </select>

    <select id="getScriptServiceVersionListForAllScript" resultMap="ScriptVersionResultMap">
        SELECT i.iid,
        v.iversion,
        v.icreate_time,
        v.iupdate_time,
        i.iscript_name_zh,
        i.icategory_id,
        v.icreator_name,
        v.iupdator_name,
        c.iparent_id,
        v.isrc_script_uuid,
        v.iinfo_unique_uuid,
        v.iuse_state,
        v.is_default,
        v.iid as iversionid,
        i.iscript_source,
        t.icontent,
        isar.iaudit_user ,
        isar.iaudit_time
        FROM ieai_script_info_version v
        JOIN ieai_script_info i ON i.iunique_uuid = v.iinfo_unique_uuid
        LEFT JOIN ieai_script_category c ON i.icategory_id = c.iid
        LEFT JOIN ieai_script_info_version_text t on t.isrc_script_uuid = v.isrc_script_uuid
        left join ieai_script_audit_relation isar on v.isrc_script_uuid = isar.isrc_script_uuid and iaudit_type = 1
        WHERE (v.ideleted = 0 or v.ideleted is null)
        <if test="serviceUuid != null">
            AND i.iunique_uuid = #{serviceUuid}
        </if>
        order by v.is_default asc,v.icreate_time asc
    </select>

    <select id="getNewContentInfo" resultMap="selectContentInfo">
        SELECT icontent
        FROM ieai_script_info_version_text
        WHERE isrc_script_uuid = (
        SELECT isrc_script_uuid
        FROM ieai_script_info_version
        WHERE
        <if test="iid != null">
            iid = #{iid}
        </if>
        )
    </select>

    <select id="getScriptsWithoutVersions" resultMap="ScriptsWithoutVersionsMap">
        select isrc_script_uuid,iid from ieai_script_info_version where iinfo_unique_uuid = #{uniqueUuid} and iversion is null;
    </select>

    <update id="updateContentInfo">
        update ieai_script_info_version_text
        <trim prefix="SET" suffixOverrides=",">
            <if test="content != null">icontent = #{content},</if>
        </trim>
        WHERE isrc_script_uuid = (
        SELECT isrc_script_uuid
        FROM ieai_script_info_version
        WHERE
        <if test="oldId != null">
            iid = #{oldId}
        </if>
        )
    </update>

    <insert id="insertExecTime" parameterType="com.ideal.script.model.entity.Exectime" useGeneratedKeys="true" keyProperty="id">
        insert into ieai_script_exectime
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                iid,
            </if>
            <if test="successTimes != null">isuccess_times,</if>
            <if test="totalTimes != null">itotal_times,</if>
            <if test="srcScriptUuid != null">isrc_script_uuid,</if>
            <if test="taskCount != null">itask_count,</if>
            icreate_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="successTimes != null">#{successTimes},</if>
            <if test="totalTimes != null">#{totalTimes},</if>
            <if test="srcScriptUuid != null">#{srcScriptUuid},</if>
            <if test="taskCount != null">#{taskCount},</if>
            ${@com.ideal.common.util.DbUtils@getCurrentTime()},
        </trim>
    </insert>

    <select id="getScriptNameAndUuid"  parameterType="com.ideal.script.model.bean.MyScriptBean" resultMap="selectMyScriptListMap">
        select
            a.iscript_name_zh,
            a.iunique_uuid,
            b.isrc_script_uuid
        from
            ieai_script_info a,
            ieai_script_info_version b
        <where>
            a.iunique_uuid = b.iinfo_unique_uuid
            <if test=" srcScriptUuid != null and srcScriptUuid != '' ">
                and b.isrc_script_uuid =#{srcScriptUuid}
            </if>
            <if test="scriptInfoVersionId != null">
                and b.iid =#{scriptInfoVersionId}
            </if>
        </where>
    </select>

    <select id="getExecTimeCountBySrcScriptUuid" parameterType="java.lang.String" resultType="java.lang.Integer">
        select count(iid) from ieai_script_exectime where isrc_script_uuid =  #{srcScriptUuid}
    </select>


    <select id="getScriptInfoByProductId" parameterType="java.lang.Long" resultMap="itsmChildrenMap">
        SELECT p.iid,si.iscript_name_zh,si.iscript_name,si.icategory_path,si.iplatform,si.iexecuser
        FROM ieai_script_info si
                 JOIN ieai_script_info_version v ON si.iunique_uuid = v.iinfo_unique_uuid
                 JOIN ieai_script_itsm_product_info p ON v.isrc_script_uuid = p.isrc_script_uuid
                 JOIN ieai_script_itsm_publish_info pub ON p.ipublish_info_id = pub.iid
        WHERE pub.iproduct_id = #{productId}
    </select>

    <select id="getScriptInfoByUniqueUuid" parameterType="java.lang.String" resultMap="selectMyScriptListMap">
        SELECT iid as iscript_info_id from ieai_script_info where iunique_uuid in
        <foreach collection="uniqueUuids" item="uniqueUuid" separator="," open="(" close=")">
            #{uniqueUuid}
        </foreach>
    </select>

</mapper>