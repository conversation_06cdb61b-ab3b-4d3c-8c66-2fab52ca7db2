package com.ideal.script.service.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ideal.common.util.BeanUtils;
import com.ideal.common.util.EncryptUtils;
import com.ideal.common.util.PageDataUtil;
import com.ideal.script.dto.ParameterValidationDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.mapper.ParameterCheckMapper;
import com.ideal.script.mapper.ParameterMapper;
import com.ideal.script.model.bean.ParameterValidationBean;
import com.ideal.script.model.dto.ParameterDto;
import com.ideal.script.model.dto.ParameterValidationResultDto;
import com.ideal.script.model.entity.InfoVersion;
import com.ideal.script.model.entity.Parameter;
import com.ideal.script.model.entity.ParameterCheck;
import com.ideal.script.model.entity.TaskParams;
import com.ideal.script.service.IParameterService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 【参数】Service业务层处理
 *
 * <AUTHOR>
 */
@Transactional(rollbackFor = Exception.class)
@Service
public class ParameterServiceImpl implements IParameterService {

    private final ParameterMapper parameterMapper;
    private final ParameterCheckMapper parameterCheckMapper;

    public ParameterServiceImpl(ParameterMapper parameterMapper, ParameterCheckMapper parameterCheckMapper) {
        this.parameterMapper = parameterMapper;
        this.parameterCheckMapper = parameterCheckMapper;
    }

    /**
     * 查询【参数】
     *
     * @param id 【参数】主键
     * @return 【参数Dto】
     */
    @Override
    public ParameterDto selectParameterById(Long id) {
        Parameter parameter = parameterMapper.selectParameterById(id);
        return BeanUtils.copy(parameter, ParameterDto.class);
    }

    /**
     * 查询【参数】列表
     *
     * @param parameterDto 【参数Dto】
     * @return 【参数列表】
     */
    @Override
    public PageInfo<ParameterDto> selectParameterList(ParameterDto parameterDto, int pageNum, int pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<Parameter> parameterList = new ArrayList<>();
        if (null != parameterDto) {
            Parameter parameter = BeanUtils.copy(parameterDto, Parameter.class);
            parameterList = parameterMapper.selectParameterList(parameter);
        }
        return PageDataUtil.toDtoPage(parameterList, ParameterDto.class);
    }

    /**
     * 新增【参数】
     *
     * @param parameterDto 【参数Dto】
     */
    @Override
    public void insertParameter(ParameterDto parameterDto) {
        Parameter parameter = BeanUtils.copy(parameterDto, Parameter.class);
        parameterMapper.insertParameter(parameter);
    }

    /**
     * 修改【参数】
     *
     * @param parameterDto 【参数Dto】
     */
    @Override
    public void updateParameter(ParameterDto parameterDto) {
        Parameter parameter = BeanUtils.copy(parameterDto, Parameter.class);
        parameterMapper.updateParameter(parameter);
    }

    /**
     * 批量删除【参数】
     *
     * @param ids 需要删除的【参数】主键
     */
    @Override
    public void deleteParameterByIds(Long[] ids) {
        parameterMapper.deleteParameterByIds(ids);
    }

    /**
     * 删除【参数】信息
     *
     * @param id 【参数 】主键
     * @return 结果
     */
    @Override
    public int deleteParameterById(Long id) {
        return parameterMapper.deleteParameterById(id);
    }

    /**
     * 根据脚本id获取脚本参数
     *
     * @param scriptId 脚本id
     * @return List<ParameterDto>
     * @see IParameterService#selectParameterList(Long)
     */
    @Override
    public List<ParameterDto> selectParameterList(Long scriptId) {
        List<Parameter> parameterList = parameterMapper.selectParameterListByScriptId(scriptId);
        return BeanUtils.copy(parameterList, ParameterDto.class);
    }

    /**
     * 根据版本uuid,获取脚本参数
     *
     * @param srcScriptUuid 版本uuid
     * @return List<Parameter>
     */
    @Override
    public List<Parameter> getParameterByUuid(String srcScriptUuid) {
        return parameterMapper.getParameterByUuid(srcScriptUuid);
    }


    /**
     * 保存参数信息
     *
     * @param scriptInfoDto 脚本信息Dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void createParameters(ScriptInfoDto scriptInfoDto) {
        if(scriptInfoDto.getScriptVersionDto().getParameterValidationDtoList() != null) {
            List<Parameter> parameterList = new ArrayList<>();
            for (ParameterValidationDto  parameterValidationDto: scriptInfoDto.getScriptVersionDto().getParameterValidationDtoList()) {
                //判断每条数据不能为空
                if(parameterValidationDto != null) {
                    Parameter parameter = BeanUtils.copy(parameterValidationDto, Parameter.class);
                    parameter.setSrcScriptUuid(scriptInfoDto.getScriptVersionDto().getSrcScriptUuid());
                    parameterList.add(parameter);
                }
            }
            for (Parameter parameter : parameterList) {
                //判断每个参数不能为null
                if(parameter != null) {
                    parameter.setId(null);
                    parameterMapper.insertParameter(parameter);
                }
            }
        }
    }
    /**
     * 参数验证规则验证参数
     *
     * @param scriptInfoDto 脚本信息
     * @return 验证结果
     */
    public ParameterValidationResultDto validateParameter(ScriptInfoDto scriptInfoDto) {
        ParameterValidationResultDto parameterValidationResultDto = new ParameterValidationResultDto();
        if (scriptInfoDto.getScriptVersionDto().getParameterValidationDtoList() == null) {
            return parameterValidationResultDto;
        }

        int line = 1;
        for (ParameterValidationDto parameterValidationDto : scriptInfoDto.getScriptVersionDto().getParameterValidationDtoList()) {
            if (parameterValidationDto != null) {
                if (!isParameterValid(parameterValidationDto)) {
                    parameterValidationResultDto.setLine(line);
                    return parameterValidationResultDto;
                }
                line++;
            }
        }
        return parameterValidationResultDto;
    }

    /**
     * 根据参数验证规则验证参数
     * @param parameterValidationDto 参数实体
     * @return  验证结果
     */
    private boolean isParameterValid(ParameterValidationDto parameterValidationDto) {
        String parameterValue = parameterValidationDto.getParamDefaultValue();
        //如果是加密的参数替换为解密的数据
        if(Objects.equals(parameterValidationDto.getParamType(), "Cipher")){
            parameterValue = EncryptUtils.sm4Decrypt(parameterValidationDto.getParamDefaultValue());
        }
        if(parameterValidationDto.getParamCheckIid() == null){
            return true;
        }
        ParameterCheck parameterCheck = parameterCheckMapper.selectParameterCheckById(parameterValidationDto.getParamCheckIid());

        if (parameterCheck == null || parameterCheck.getCheckRule() == null) {
            return true;
        }

        String checkValue = parameterCheck.getCheckRule();
        Pattern pattern = Pattern.compile(checkValue, Pattern.DOTALL);
        Matcher matcher = pattern.matcher(parameterValue);

        return matcher.find();
    }


    /**
     * 获取参数
     *
     * @param infoVersion 脚本版本信息
     * @return 参数数组
     */
    public List<ParameterValidationDto> getParameterValidationDtos(InfoVersion infoVersion) {
        //获取参数
        Parameter parameter = new Parameter();
        parameter.setSrcScriptUuid(infoVersion.getSrcScriptUuid());
        List<ParameterValidationBean> parameterValidationBeans = parameterMapper.selectParameterValidationList(parameter);
        return BeanUtils.copy(parameterValidationBeans, ParameterValidationDto.class);
    }

    @Override
    public List<TaskParams> getParameterByTaskId(Long taskId) {
        return parameterMapper.getParameterByTaskId(taskId);
    }
}
