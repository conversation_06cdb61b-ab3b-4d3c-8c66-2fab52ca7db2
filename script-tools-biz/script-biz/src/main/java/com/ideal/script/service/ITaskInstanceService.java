package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.model.dto.TaskInstanceDto;

import java.util.List;
import java.util.Map;

/**
 * 脚本任务运行实例Service接口
 * 
 * <AUTHOR>
 */
 public interface ITaskInstanceService
{
    /**
     * 查询脚本任务运行实例
     * 
     * @param id 脚本任务运行实例主键
     * @return 脚本任务运行实例
     */
    TaskInstanceDto selectTaskInstanceById(Long id);

    /**
     * 查询任务实例的总数
     * @param id instanceId
     * @return 查询任务实例的总数
     */
    Long selectIpsCount(Long id);

    /**
     * 根据id查询instance实例
     * @param ids instanceId集合
     * @return instanceId集合
     */
    List<Long> selectTaskInstanceIdsById(Long [] ids);

    /**
     * 查询脚本任务运行实例
     *
     * @param id 脚本任务运行实例主键
     * @return 脚本任务运行实例
     */
    TaskInstanceDto selectTaskInstanceByTaskId(Long id);
    /**
     * 通过taskInfoId查询instance信息
     * @param taskInfoId 任务id
     * @return 任务实例对象
     */
    TaskInstanceDto getTaskInstanceByTaskInfoId(Long taskInfoId);

    /**
     * 根据agent实例id获取任务实例数据
     * @param runtimeId agent实例id
     * @return 任务实例对象
     */
    TaskInstanceDto getTaskInstanceByRuntimeId(Long runtimeId);

    /**
     * 查询脚本任务运行实例列表
     *
     * @param pageNum 当前页
     * @param pageSize 每页条数
     * @param taskInstanceDto 脚本任务运行实例
     * @return 脚本任务运行实例集合
     */
     PageInfo<TaskInstanceDto> selectTaskInstanceList(TaskInstanceDto taskInstanceDto, int pageNum, int pageSize);

    /**
     * 新增脚本任务运行实例
     * 
     * @param taskInstanceDto 脚本任务运行实例
     * @return 结果
     */
     int insertTaskInstance(TaskInstanceDto taskInstanceDto);

    /**
     * 修改脚本任务运行实例
     * 
     * @param taskInstanceDto 脚本任务运行实例
     * @return 结果
     */
     int updateTaskInstance(TaskInstanceDto taskInstanceDto);

    /**
     * 批量删除脚本任务运行实例
     * 
     * @param ids 需要删除的脚本任务运行实例主键集合
     * @return 结果
     */
     int deleteTaskInstanceByIds(Long[] ids);

    /**
     * 删除脚本任务运行实例信息
     * 
     * @param id 脚本任务运行实例主键
     * @return 结果
     */
     int deleteTaskInstanceById(Long id);


    /**
     * 功能描述：更新serverNum
     *
     * @param taskInstanceId 任务实例Id
     * @param notInStates 排除的状态
     */
    void updateServerNum(Long taskInstanceId, List<Integer> notInStates);

    /**
     * 功能描述：统计完成、异常、略过、终止数量
     *
     * @param taskInstanceId 任务实例id
     * @return  List
     */
    List<Map<String,Object>> getStatusSummary(Long taskInstanceId);

    /**
     * 功能描述：更新脚本任务运行实例表的任务状态
     *
     * @param state 状态
     * @param taskInstanceId 任务实例Id
     * @param excludeStates 排除状态
     * @param updateEndTime 是否更新结束时间
     */
    int updateState(int state, Long taskInstanceId, int[] excludeStates,boolean updateEndTime);
    /**
     * 开启新事务，根据统计状态，更新任务状态
     *
     * @param taskInstanceId 任务实例id
     */
    void updateTaskInstanceStateNewTransaction(Long taskInstanceId);

    /**
     * 功能描述： 更新脚本任务实例表RunNum数量
     *
     * @param taskInstanceId 任务实例Id
     */
    void updateRunNum(Long taskInstanceId);

    /**
     * 功能描述：更新脚本任务实例表结束时间
     *
     * @param taskInstanceId 脚本实例表主键
     * @param notInStates 不包含分批执行的状态
     * <AUTHOR>
     */
    void updateEndTime(Long taskInstanceId, List<Integer> notInStates);

    /**
     * 更新任务实例状态(忽略/终止时调用、计数器为0调用)
     *
     * @param taskInstanceId 任务实例id
     */
    void updateTaskInstanceState(Long taskInstanceId);

}
