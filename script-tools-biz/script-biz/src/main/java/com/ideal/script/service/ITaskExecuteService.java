package com.ideal.script.service;

import com.github.pagehelper.PageInfo;
import com.ideal.script.dto.RetryScriptInstanceApiDto;
import com.ideal.script.dto.StopScriptTasksApiDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.model.bean.StartAgentCommonParam;
import com.ideal.script.model.bean.StartAgentParams;
import com.ideal.script.model.dto.ScriptStopShellDto;
import com.ideal.script.model.dto.ScriptTestExecutionDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskExecuteDto;
import com.ideal.script.model.dto.TaskExecuteQueryDto;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.model.entity.TaskRuntime;
import com.ideal.system.common.component.model.CurrentUser;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * 执行任务Service接口
 *
 * <AUTHOR>
 */

public interface ITaskExecuteService {

    /**
     * 待执行任务列表
     *
     * @param pageNum        当前页
     * @param pageSize       每页条数
     * @param currentUser    当前用户
     * @param taskExecuteDto 查询条件Dto
     * @return PageInfo<TaskExecuteDto>
     */
    PageInfo<TaskExecuteDto> selectTaskReadyToExecuteList(TaskExecuteQueryDto taskExecuteDto, Integer pageNum, Integer pageSize, CurrentUser currentUser);

    /**
     * 查询定时、周期维护任务列表
     * @param taskExecuteQueryDto 查询条件dto
     * @param pageNum 页码
     * @param pageSize  分页大小
     * @param currentUser 当前用户
     * @return PageInfo<TaskExecuteDto>
     */
    PageInfo<TaskExecuteDto> selectTimeTasks(TaskExecuteQueryDto taskExecuteQueryDto, Integer pageNum, Integer pageSize, CurrentUser currentUser);

    /**
     * 定时、周期启停切换
     * @param scriptTaskId 脚本服务化任务id
     * @param state 状态
     * @throws ScriptException 脚本服务化异常
     */
    void timeTaskSwitch(long scriptTaskId, int state) throws ScriptException;

    /**
     * 定时、周期任务终止
     * @param scriptTaskId 脚本服务化任务id
     * @throws ScriptException 脚本服务化异常
     */
    void timeTaskKill(long scriptTaskId) throws ScriptException;

    /**
     * 定制、周期修改cron、执行时间
     * @param taskExecuteDto 参数dto
     * @throws ScriptException 脚本服务化异常
     */
    void updateTimeTaskCron(TaskExecuteDto taskExecuteDto) throws ScriptException;

    /**
     * 启动脚本任务
     *
     * @param taskStartDto 任务启动封装参数Dto
     * @param user         用户
     * @return Long 脚本任务运行实例ID（ieai_script_task_instance表Id)
     * @throws ScriptException 抛出自定义通知异常
     */
    Long scriptTaskStart(TaskStartDto taskStartDto, CurrentUser user) throws ScriptException;
    /**
     * 功能描述：将选定的agent标记为已执行状态，并将本次脚本任务的执行批次号加一进行更新
     * 同时返回任务执行相关的信息，包括勾选的AgentId集合以及脚本任务指定运行的agent总数
     *
     * @param taskStartDto 任务启动封装参数Dto
     * <AUTHOR>
     */
    StartAgentCommonParam updateBatchStartAndCheck(TaskStartDto taskStartDto) throws ScriptException;

    /**
     * 查询当前用户可见的正在的运行任务列表。
     *
     * @param pageNum     当前页
     * @param pageSize    每页条数
     * @param currentUser 用户
     * @param queryParam  查询条件的封装对象，包含页面查询条件属性（适用于“运行中”选项卡）
     * @return PageInfo<TaskExecuteDto>
     */
    PageInfo<TaskExecuteDto> listRunningScriptTasks(TaskExecuteQueryDto queryParam, Integer pageNum, Integer pageSize, CurrentUser currentUser);


    /**
     * 查询当前用户可见的正在运行任务列表。
     *
     * @param pageNum     当前页
     * @param pageSize    每页条数
     * @param queryParam  查询条件的封装对象，包含页面查询条件属性（适用于“执行历史”选项卡）
     * @param currentUser 用户
     * @return R<PageInfo < TaskExecuteDto>> 包含已完成任务信息的分页结果对象
     */
    PageInfo<TaskExecuteDto> listCompleteScriptTasks(TaskExecuteQueryDto queryParam, Integer pageNum, Integer pageSize, CurrentUser currentUser);


    /**
     * 功能描述：agent实例重试
     *
     * @param id             agent运行实例id
     * @param user           用户
     * @param taskInstanceId 脚本任务实例主键
     * @throws ScriptException 抛出自定义通知异常
     */
    void retryScriptServiceShell(Long id, Long taskInstanceId, CurrentUser user) throws ScriptException;

    /**
     *  获取agent实例数据
     * @param retryScriptInstanceApiDto 参数
     * @return agent实例对象
     */
    TaskRuntime getTaskRuntime(RetryScriptInstanceApiDto retryScriptInstanceApiDto);

    /**
     * 功能描述：agent实例终止
     *
     * @param taskInstanceId 脚本任务运行实例Id
     * @param runTimeIds     agent运行实例id集合
     * @throws ScriptException 抛出自定义通知异常
     */
    void scriptShellKill(Long taskInstanceId, Long[] runTimeIds) throws ScriptException;

    /**
     * 脚本测试
     *
     * @param testExecutionDto 脚本测试封装的参数信息
     * @param user             用户
     * @return Long
     * @throws ScriptException 抛出自定义通知异常
     */
    Long scriptTestExecution(ScriptTestExecutionDto testExecutionDto, CurrentUser user) throws ScriptException;

    /**
     * 启动脚本任务
     *
     * @param taskStartDto 任务启动封装参数Dto
     * @param user         用户
     * @return Long 脚本任务运行实例ID（ieai_script_task_instance表Id)
     * @throws ScriptException 抛出自定义通知异常
     */
    Long scriptTaskStartFormApply(TaskStartDto taskStartDto, CurrentUser user) throws ScriptException;

    /**
     * 功能描述：略过agent
     *
     * @param taskInstanceId 脚本任务运行实例Id
     * @param runTimeIds     agent运行实例id集合
     * @throws ScriptException 抛出自定义通知异常
     */
    void skipScriptShell(Long taskInstanceId, Long[] runTimeIds) throws ScriptException;

    /**
     * 功能描述：任务终止（取消）
     *
     * @param taskInstanceId 脚本任务运行实例Id
     * @throws ScriptException 抛出自定义通知异常
     */
    void stopTask(Long [] taskInstanceId) throws ScriptException;

    /**
     * 功能描述：终止
     *
     * @param taskInstanceId 脚本任务运行实例Id
     * @param runTimeIds     agent运行实例id集合
     * @throws ScriptException 抛出自定义通知异常
     */
    void scriptShellKillByRunTimeIds(Long taskInstanceId, Long[] runTimeIds) throws ScriptException;

    /**
     * 白名单任务启动
     * @param user 用户
     * @param taskInfo 脚本任务信息
     * @param srcScriptUuid 脚本版本uuid
     * @throws ScriptException 抛出自定义通知异常
     * @return Long
     */
    Long scriptWhiteTaskStart(CurrentUser user, TaskDto taskInfo, String srcScriptUuid) throws ScriptException;

    /**
     * 待执行页面取消任务
     *
     * @param scriptTaskId 脚本任务Id
     * @throws ScriptException    自定义异常
     * <AUTHOR>
     */
    void cancelTask(Long scriptTaskId) throws ScriptException;

    /**
     * 功能描述： 将参数对象转换为JSON字符串
     *
     * @param params params 要转换的对象
     * @return {@link String } JSON字符串表示
     * <AUTHOR>
     */
     String convertObjectToJsonString(List<Object> params);

    /**
     * 获取实时输出
     *
     * @param taskRuntimeId agent运行实例Id
     * @return {@link String }
     * <AUTHOR>
     */
    String getRealTimeOutPutMessage(Long taskRuntimeId);

    /**
     * 终止任务
     *
     * @param taskInstanceId agent运行实例Id
     * <AUTHOR>
     * @throws ScriptException 脚本服务化异常
     * @return StopScriptTasksApiDto 返回值dto
     */
    StopScriptTasksApiDto stopScriptTaskByTaskInstanceId(List<Long> taskInstanceId) throws ScriptException;

    /**
     * 终止其它模块脚本任务
     * @param stopCallerScriptTaskApiDtos 参数
     * @return 终止成功的agent信息
     */
    List<String> stopScriptTaskByCallerTaskIdAndAgent(List<RetryScriptInstanceApiDto> stopCallerScriptTaskApiDtos) throws ScriptException;

    /**
     * 导出
     * @param ids 基础信息表id列表
     * @param response 响应
     */
    void exportExcel(List<Long> ids, HttpServletResponse response);

    /**
     * 任务监控执行历史导出excel
     * @param taskInstanceIds 任务实例id
     * @param response 响应
     * @throws IOException 异常
     */
    void exportAgentHisExcel(List<Long> taskInstanceIds, HttpServletResponse response) throws IOException;

    /**
     * 终止agent实例
     * @param scriptStopShellDto 参数
     */
    void stopScriptInstanceShell(ScriptStopShellDto scriptStopShellDto) throws ScriptException;
    /**
     * 功能描述： 执行启动脚本任务
     *
     * @param taskInstanceId        脚本任务运行实例表主键
     * @param taskStartDto          任务启动Dto
     * @param agents                启动的agent数据对象
     * @param user                  用户
     * @param taskTaskDto           任务dto
     * @return Long
     */
    Long execScriptTaskStart(Long taskInstanceId, TaskStartDto taskStartDto, List<StartAgentParams> agents, CurrentUser user, TaskDto taskTaskDto) throws ScriptException;
}
