package com.ideal.script.service.impl;

import com.alibaba.fastjson.JSON;

import com.ideal.jobapi.core.apiclient.IdealXxlJobApiUtil;
import com.ideal.jobapi.core.enums.IdealStateType;
import com.ideal.jobapi.core.model.IdealXxlJobInfoDto;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.exception.ScheduleJobOperateException;
import com.ideal.script.model.dto.ScheduleJobTaskDto;
import com.ideal.script.service.JobOperateService;
import com.xxl.job.core.biz.model.ReturnT;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 操作定时驱动服务
 *
 * <AUTHOR>
 */

@Service
public class JobOperateServiceImpl implements JobOperateService {

    private static final Logger logger = LoggerFactory.getLogger(JobOperateServiceImpl.class);

    @Override
    public Integer createAndStartJob(ScheduleJobTaskDto scheduleJobTaskDto) throws ScheduleJobOperateException {
        Integer xxJobId;
        IdealXxlJobInfoDto idealJobInfoModel = IdealXxlJobInfoDto.getSimpleCronInstance(scheduleJobTaskDto.getTaskName(), "scriptJobHandler", scheduleJobTaskDto.getCreateName(), scheduleJobTaskDto.getCron());
        idealJobInfoModel.setIdealStateType(IdealStateType.RUNNING);
        idealJobInfoModel.setIdealExecutorParam(JSON.toJSONString(scheduleJobTaskDto));
        ReturnT<String> sd = IdealXxlJobApiUtil.addJob(idealJobInfoModel);
        if (sd.getCode() != Enums.HttpStatusCode.OK.getCode()) {
            logger.info("creat task name is: {},result is fail,return code:{},error message:{}", scheduleJobTaskDto.getTaskName(), sd.getCode(), sd.getMsg());
            throw new ScheduleJobOperateException(String.valueOf(sd.getCode()), sd.getMsg());
        }
        logger.info("job returns parameters:{}", sd);
        if (sd.getContent() == null) {
            logger.error("Task xxJob id is null!");
            throw new ScheduleJobOperateException("xxjob.id.null.error");
        }
        xxJobId = Integer.valueOf(sd.getContent());
        logger.info("create task name is [{}] success，schedule task id:{}", scheduleJobTaskDto.getTaskName(), xxJobId);
        return xxJobId;
    }

    @Override
    public Boolean startJob(Integer xxJobTaskId) {
        if (xxJobTaskId == null || xxJobTaskId <= 0) {
            logger.error("start schedule task id is null!");
            return false;
        }
        ReturnT<String> startResult = IdealXxlJobApiUtil.startJob(xxJobTaskId);
        if (startResult.getCode() !=  Enums.HttpStatusCode.OK.getCode()) {
            logger.error("start schedule task id {}  is fail! cause is {}", xxJobTaskId, startResult.getMsg());
            return false;
        }
        return true;
    }


    @Override
    public boolean modifyJob(ScheduleJobTaskDto scheduleJobTaskDto) {
        IdealXxlJobInfoDto idealJobInfoModel = IdealXxlJobInfoDto.getSimpleCronInstance(scheduleJobTaskDto.getTaskName(), "scriptJobHandler", scheduleJobTaskDto.getCreateName(), scheduleJobTaskDto.getCron());
        idealJobInfoModel.setId(Math.toIntExact(scheduleJobTaskDto.getScheduleJobId()));
        idealJobInfoModel.setIdealExecutorParam(JSON.toJSONString(scheduleJobTaskDto));
        ReturnT<String> sd = IdealXxlJobApiUtil.updateJob(idealJobInfoModel);
        logger.info("modify schedule task id {} ! response message is {}", scheduleJobTaskDto.getScheduleJobId(), sd.getMsg());
        return sd.getCode() ==  Enums.HttpStatusCode.OK.getCode();
    }

    @Override
    public boolean stopJob(Integer xxJobTaskId) {
        ReturnT<String> sd = IdealXxlJobApiUtil.stopJob(xxJobTaskId);
        logger.info("stop schedule task id {} ! response message is {}", xxJobTaskId, sd.getMsg());
        return sd.getCode() ==  Enums.HttpStatusCode.OK.getCode();
    }
}
