package com.ideal.script.service.impl;

import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.mapper.TaskIpsMapper;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.entity.AgentInfo;
import com.ideal.script.service.IAgentInfoService;
import org.apache.ibatis.session.SqlSession;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class TaskIpsServiceImplTest {

    @Mock
    private TaskIpsMapper mockTaskIpsMapper;
    @Mock
    private IAgentInfoService mockAgentInfoService;
    @Mock
    private BatchDataUtil mockBatchDataUtil;
    @Mock
    private AsyncAgentQueryService mockAsyncAgentQueryService;
    @Mock
    private SqlSession mockSqlSession;

    @Spy
    @InjectMocks
    private TaskIpsServiceImpl taskIpsServiceImplUnderTest;

    private ScriptExecAuditDto scriptExecAuditDto;
    private TaskDto taskInfo;
    private List<AgentInfoDto> agentUsers;
    private List<AgentInfo> existingAgents;

    @BeforeEach
    void setUp() throws Exception {
        // 初始化测试数据
        setupTestData();
    }

    private void setupTestData() {
        // 初始化ScriptExecAuditDto
        scriptExecAuditDto = new ScriptExecAuditDto();
        scriptExecAuditDto.setExecuser("testUser");

        // 初始化TaskDto
        taskInfo = new TaskDto();
        taskInfo.setId(100L);

        // 初始化AgentInfoDto列表
        agentUsers = new ArrayList<>();
        AgentInfoDto agent1 = new AgentInfoDto();
        agent1.setId(1L);
        agent1.setAgentIp("***********");
        agent1.setAgentPort(8080);
        agent1.setExecUserName("agent1User");
        agentUsers.add(agent1);

        AgentInfoDto agent2 = new AgentInfoDto();
        agent2.setId(2L);
        agent2.setAgentIp("***********");
        agent2.setAgentPort(8080);
        agent2.setExecUserName("");
        agentUsers.add(agent2);

        // 初始化已存在的AgentInfo列表
        existingAgents = new ArrayList<>();
        AgentInfo existingAgent = new AgentInfo();
        existingAgent.setId(1L);
        existingAgent.setAgentIp("***********");
        existingAgent.setAgentPort(8080);
        existingAgents.add(existingAgent);
    }

    @ParameterizedTest
    @MethodSource("saveTaskIpsTestParams")
    @DisplayName("测试saveTaskIps方法-参数化测试")
    void saveTaskIps_test(String testCase, List<AgentInfoDto> chosedAgentUsers) throws Exception {
        // Setup
        scriptExecAuditDto.setChosedAgentUsers(chosedAgentUsers);

        if (chosedAgentUsers != null && !chosedAgentUsers.isEmpty()) {
            // Mock SqlSession.getMapper
            when(mockSqlSession.getMapper(TaskIpsMapper.class)).thenReturn(mockTaskIpsMapper);

            // Mock AsyncAgentQueryService的异步查询
            CompletableFuture<List<AgentInfo>> future = CompletableFuture.completedFuture(existingAgents);
            when(mockAsyncAgentQueryService.queryAgentsBatchAsync(any(List.class))).thenReturn(future);

            // Mock Batch接口的batchData方法，避免Spring上下文调用
            doNothing().when(taskIpsServiceImplUnderTest).batchData(any(List.class), any());
        }

        // Execute and Verify
        assertDoesNotThrow(() -> {
            taskIpsServiceImplUnderTest.saveTaskIps(scriptExecAuditDto, taskInfo, mockSqlSession);
        });
    }

    static Stream<Object[]> saveTaskIpsTestParams() {
        // 创建测试用的AgentInfoDto列表
        List<AgentInfoDto> normalAgentUsers = new ArrayList<>();
        AgentInfoDto agent1 = new AgentInfoDto();
        agent1.setId(1L);
        agent1.setAgentIp("***********");
        agent1.setAgentPort(8080);
        agent1.setExecUserName("agent1User");
        normalAgentUsers.add(agent1);

        AgentInfoDto agent2 = new AgentInfoDto();
        agent2.setId(2L);
        agent2.setAgentIp("***********");
        agent2.setAgentPort(8080);
        agent2.setExecUserName("");
        normalAgentUsers.add(agent2);

        return Stream.of(
            new Object[]{"空Agent列表", Collections.emptyList()},
            new Object[]{"null Agent列表", null},
            new Object[]{"正常Agent列表-成功", normalAgentUsers}
        );
    }





}
