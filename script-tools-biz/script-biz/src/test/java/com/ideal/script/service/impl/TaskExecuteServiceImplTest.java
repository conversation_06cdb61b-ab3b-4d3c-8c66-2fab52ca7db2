package com.ideal.script.service.impl;

import cn.idev.excel.EasyExcel;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageInfo;
import com.ideal.agent.gateway.api.AgentOperateApi;
import com.ideal.agent.gateway.exception.SendException;
import com.ideal.agent.gateway.exception.TaskSaveException;
import com.ideal.agent.gateway.model.AgentOperateDto;
import com.ideal.agent.gateway.model.AgentOptionDto;
import com.ideal.agent.gateway.model.AgentSyncOperateResultDto;
import com.ideal.agent.gateway.model.DynamicResourcesDto;
import com.ideal.agent.gateway.model.TargetTypeEnum;
import com.ideal.agent.management.api.AgentManagementApi;
import com.ideal.common.util.batch.BatchHandler;
import com.ideal.common.util.spring.SpringUtil;
import com.ideal.sc.util.Base64;
import com.ideal.sc.util.CurrentUserUtil;
import com.ideal.script.common.constant.Constants;
import com.ideal.script.common.constant.enums.AgentExecuteKey;
import com.ideal.script.common.constant.enums.Enums;
import com.ideal.script.common.util.BatchDataUtil;
import com.ideal.script.common.util.DesUtils;
import com.ideal.script.config.ScriptBusinessConfig;
import com.ideal.script.dto.AttachmentDto;
import com.ideal.script.dto.CategoryApiDto;
import com.ideal.script.dto.CategoryDto;
import com.ideal.script.dto.ScriptInfoDto;
import com.ideal.script.dto.ScriptVersionDto;
import com.ideal.script.dto.StopScriptTasksApiDto;
import com.ideal.script.exception.ScriptException;
import com.ideal.script.mapper.AttachmentMapper;
import com.ideal.script.mapper.InfoVersionTextMapper;
import com.ideal.script.mapper.TaskAttachmentMapper;
import com.ideal.script.mapper.TaskInstanceMapper;
import com.ideal.script.mapper.TaskIpsMapper;
import com.ideal.script.mapper.TaskMapper;
import com.ideal.script.mapper.TaskParamsMapper;
import com.ideal.script.mapper.TaskRuntimeMapper;
import com.ideal.script.model.bean.ShellCmdOutput;
import com.ideal.script.model.bean.StartAgentCommonParam;
import com.ideal.script.model.bean.StartAgentParams;
import com.ideal.script.model.bean.TaskExecuteBean;
import com.ideal.script.model.bean.TaskIpsAgentResultBean;
import com.ideal.script.model.bean.TaskRunTimeBindAgentBean;
import com.ideal.script.model.bean.TimeTaskExportBean;
import com.ideal.script.model.dto.AgentInfoDto;
import com.ideal.script.model.dto.ScriptExecAuditDto;
import com.ideal.script.model.dto.ScriptTestExecutionDto;
import com.ideal.script.model.dto.TaskDto;
import com.ideal.script.model.dto.TaskExecuteDto;
import com.ideal.script.model.dto.TaskExecuteQueryDto;
import com.ideal.script.model.dto.TaskGroupsDto;
import com.ideal.script.model.dto.TaskHisAgentExcelDto;
import com.ideal.script.model.dto.TaskInstanceDto;
import com.ideal.script.model.dto.TaskParamsDto;
import com.ideal.script.model.dto.TaskRuntimeDto;
import com.ideal.script.model.dto.TaskScheduleDto;
import com.ideal.script.model.dto.TaskStartDto;
import com.ideal.script.model.entity.Category;
import com.ideal.script.model.entity.InfoVersionText;
import com.ideal.script.model.entity.Task;
import com.ideal.script.model.entity.TaskAttachment;
import com.ideal.script.model.entity.TaskParams;
import com.ideal.script.model.entity.TaskRuntime;
import com.ideal.script.observer.itsm.ItsmScriptTaskResultPush;
import com.ideal.script.service.IAgentInfoService;
import com.ideal.script.service.ICategoryService;
import com.ideal.script.service.IExectimeService;
import com.ideal.script.service.IInfoService;
import com.ideal.script.service.IInfoVersionService;
import com.ideal.script.service.ITaskAttachmentService;
import com.ideal.script.service.ITaskExecuteService;
import com.ideal.script.service.ITaskGroupsService;
import com.ideal.script.service.ITaskInstanceService;
import com.ideal.script.service.ITaskIpsService;
import com.ideal.script.service.ITaskRuntimeService;
import com.ideal.script.service.ITaskScheduleService;
import com.ideal.script.service.ITaskService;
import com.ideal.script.service.JobOperateService;
import com.ideal.snowflake.util.SnowflakeIdWorker;
import com.ideal.system.common.component.model.CurrentUser;
import com.thoughtworks.xstream.XStream;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedConstruction;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.mockito.stubbing.Answer;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RMap;
import org.redisson.api.RQueue;
import org.redisson.api.RedissonClient;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import javax.crypto.IllegalBlockSizeException;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.anyList;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.spy;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class TaskExecuteServiceImplTest {

    private static MockedStatic<SpringUtil> springUtilMockedStatic;
    private static MockedStatic<SnowflakeIdWorker> snowflakeIdWorkerMockedStatic;

    @Mock
    private RedissonClient mockRedissonClient;
    @Mock
    private ITaskService mockTaskService;
    @Mock
    private TaskParamsMapper mockTaskParamsMapper;
    @Mock
    private TaskIpsMapper mockTaskIpsMapper;
    @Mock
    private IInfoService mockInfoService;
    @Mock
    private IInfoVersionService mockInfoVersionService;
    @Mock
    private ITaskInstanceService mockTaskInstanceService;
    @Mock
    private SqlSessionFactory mockFactory;
    @Mock
    private BatchDataUtil mockBatchDataUtil;
    @Mock
    private InfoVersionTextMapper mockInfoVersionTextMapper;
    @Mock
    private TaskAttachmentMapper mockTaskAttachmentMapper;
    @Mock
    private AgentOperateApi mockAgentOperateApi;
    @Mock
    private ITaskRuntimeService mockTaskRuntimeService;
    @Mock
    private ICategoryService mockCategoryService;
    @Mock
    private ITaskGroupsService mockTaskGroupsService;
    @Mock
    private ITaskAttachmentService mockTaskAttachmentService;
    @Mock
    private ITaskIpsService mockTaskIpsService;
    @Mock
    private IExectimeService mockExectimeService;
    @Mock
    private ScriptBusinessConfig scriptBusinessConfig;

    @Mock
    private AgentManagementApi agentManagementApi;
    private TaskExecuteServiceImpl taskExecuteServiceImplUnderTest;
    @Mock
    private ITaskScheduleService taskScheduleService;
    @Mock
    private JobOperateService jobOperateService;
    @Mock
    private TaskMapper taskMapper;
    @Mock
    private TaskInstanceMapper taskInstanceMapper;
    @Mock
    private ITaskExecuteService iTaskExecuteService;
    @Mock
    private AttachmentMapper attachmentMapper;
    @Mock
    private ItsmScriptTaskResultPush itsmScriptTaskResultPush;

    @Mock
    private IAgentInfoService agentInfoService;
    @Mock
    private RedisTemplate<String, String> redisTemplate;
    @Mock
    private RedissonClient redissonClient;

    @Mock
    private TaskRuntimeMapper taskRuntimeMapper;

    @BeforeEach
    void setUp() throws Exception {
        taskExecuteServiceImplUnderTest = new TaskExecuteServiceImpl(scriptBusinessConfig,
                mockRedissonClient, mockTaskService, mockTaskParamsMapper, mockTaskIpsMapper,
                mockInfoService, mockInfoVersionService, mockTaskInstanceService, mockFactory, mockBatchDataUtil,
                mockInfoVersionTextMapper, mockTaskAttachmentMapper,
                attachmentMapper, mockAgentOperateApi, mockTaskRuntimeService, mockCategoryService,
                mockTaskGroupsService,
                mockTaskAttachmentService, mockTaskIpsService, mockExectimeService, agentManagementApi,
                taskScheduleService, jobOperateService, taskMapper, taskInstanceMapper, iTaskExecuteService,
                itsmScriptTaskResultPush, agentInfoService, redisTemplate, taskRuntimeMapper);

        // 设置Redis相关Mock
        setupRedisMocks();
    }

    private void setupRedisMocks() {
        // Mock RAtomicLong
        RAtomicLong mockRAtomicLong = mock(RAtomicLong.class);
        when(mockRedissonClient.getAtomicLong(any(String.class))).thenReturn(mockRAtomicLong);
        when(mockRAtomicLong.incrementAndGet()).thenReturn(1L);
        when(mockRAtomicLong.decrementAndGet()).thenReturn(0L);
        when(mockRAtomicLong.isExists()).thenReturn(true);
        doNothing().when(mockRAtomicLong).set(anyLong());
        when(mockRAtomicLong.expire(any(Duration.class))).thenReturn(true);

        // Mock RMap
        @SuppressWarnings("unchecked")
        RMap<String, String> mockRMap = mock(RMap.class);
        when(mockRedissonClient.getMap(anyString())).thenReturn((RMap) mockRMap);
        when(mockRMap.put(anyString(), anyString())).thenReturn(null);
        doNothing().when(mockRMap).putAll(any(Map.class));
        when(mockRMap.expire(any(Duration.class))).thenReturn(true);

        // Mock RQueue
        @SuppressWarnings("unchecked")
        RQueue<Long> mockRQueue = mock(RQueue.class);
        when(mockRedissonClient.getQueue(anyString())).thenReturn((RQueue) mockRQueue);
        when(mockRQueue.addAll(any(Collection.class))).thenReturn(true);
        when(mockRQueue.expire(any(Duration.class))).thenReturn(true);
        when(mockRQueue.poll()).thenReturn(1L);

        // Mock RBucket
        when(mockRedissonClient.getBucket(any(String.class))).thenReturn(mock(RBucket.class));
    }

    private static MockedStatic<TransactionSynchronizationManager> transactionSynchronizationManagerMockedStatic;

    @BeforeAll
    static void setUpStaticMethodAll() {
        // 在所有测试方法执行前，Mock 静态方法
        transactionSynchronizationManagerMockedStatic = mockStatic(TransactionSynchronizationManager.class);
        springUtilMockedStatic = mockStatic(SpringUtil.class);
        snowflakeIdWorkerMockedStatic = mockStatic(SnowflakeIdWorker.class);

        // Mock SpringUtil.getBean() 方法，返回BatchHandler的mock对象
        BatchHandler mockBatchHandler = mock(BatchHandler.class);
        springUtilMockedStatic.when(() -> SpringUtil.getBean(BatchHandler.class)).thenReturn(mockBatchHandler);
        transactionSynchronizationManagerMockedStatic.when(TransactionSynchronizationManager::isSynchronizationActive)
                .thenReturn(true);

        // Mock SnowflakeIdWorker.generateId() 静态方法
        snowflakeIdWorkerMockedStatic.when(SnowflakeIdWorker::generateId).thenReturn(12345L);
    }

    @AfterAll
    static void tearDown() {
        // 在所有测试方法执行后，关闭 Mock
        transactionSynchronizationManagerMockedStatic.close();
        springUtilMockedStatic.close();
        snowflakeIdWorkerMockedStatic.close();
    }

    @Test
    void testSelectTaskReadyToExecuteList() {
        // Setup
        final TaskExecuteQueryDto taskExecuteQueryDto = new TaskExecuteQueryDto();
        taskExecuteQueryDto.setTaskName("taskName");
        taskExecuteQueryDto.setCategoryId(0L);

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");

        // Configure ITaskService.selectTaskReadyToExecuteList(...).
        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean.setCategoryId(0L);
        Page<TaskExecuteBean> page = new Page<>();
        page.add(taskExecuteBean);
        when(mockTaskService.selectTaskReadyToExecuteList(any(TaskExecuteBean.class),
                any(CurrentUser.class))).thenReturn(page);

        when(mockCategoryService.getCategoryFullPath(0L)).thenReturn("scriptCategoryName");

        // Run the test
        final PageInfo<TaskExecuteDto> result = taskExecuteServiceImplUnderTest.selectTaskReadyToExecuteList(
                taskExecuteQueryDto, 0, 0, currentUser);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testSelectTaskReadyToExecuteList_noCategory() {
        // Setup
        final TaskExecuteQueryDto taskExecuteQueryDto = new TaskExecuteQueryDto();
        taskExecuteQueryDto.setTaskName("taskName");
        taskExecuteQueryDto.setCategoryId(0L);

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");

        // Configure ITaskService.selectTaskReadyToExecuteList(...).
        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        Page<TaskExecuteBean> page = new Page<>();
        page.add(taskExecuteBean);
        when(mockTaskService.selectTaskReadyToExecuteList(any(TaskExecuteBean.class),
                any(CurrentUser.class))).thenReturn(page);

        // Run the test
        final PageInfo<TaskExecuteDto> result = taskExecuteServiceImplUnderTest.selectTaskReadyToExecuteList(
                taskExecuteQueryDto, 0, 0, currentUser);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testScriptTaskStart() throws Exception {
        // Setup
        final TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setRetryRuntimeId(0L);
        taskStartDto.setDriveMode(0);
        taskStartDto.setScriptTaskId(0L);
        taskStartDto.setSrcScriptUuid("srcScriptUuid");
        taskStartDto.setTaskName("scriptName");
        taskStartDto.setInfoUniqueUuid("infoUniqueUuid");
        taskStartDto.setScriptType("scriptType");
        taskStartDto.setIscriptTaskInstanceId(0L);
        taskStartDto.setTaskIps(new Long[] { 0L });
        taskStartDto.setRetry(false);
        taskStartDto.setEachNum(1);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("loginName");

        // Configure ITaskService.selectTaskById(...).
        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(0);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(0L);
        taskDto.setSrcScriptUuid("srcScriptUuid");
        taskDto.setTaskName("scriptName");
        taskDto.setEachNum(0);
        taskDto.setTaskScheduler(0);
        taskDto.setTaskCron("taskCron");
        taskDto.setPublishDesc("publishDesc");
        taskDto.setTimeout(0L);
        taskDto.setType(0);
        taskDto.setDriveMode(0);
        when(mockTaskService.selectTaskById(0L)).thenReturn(taskDto);

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType("type");
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Collections.singletonList(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(taskParams);

        when(mockTaskIpsMapper.getTotalAgentCountForTask(0L)).thenReturn(0);

        when(mockTaskIpsMapper.getMaxOperIdForTask(0L)).thenReturn(0);

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setExpectType(0);

        infoVersionDto.setExpectLastline("expectLastline");
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(infoVersionDto);

        // Configure TaskIpsMapper.getTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean1 = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean1.setScriptTaskIpsId(0L);
        taskIpsAgentResultBean1.setScriptAgentInfoId(0L);
        taskIpsAgentResultBean1.setExecUserName("execUser");
        taskIpsAgentResultBean1.setAgentIp("agentIp");
        taskIpsAgentResultBean1.setAgentPort(0);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans1 = Arrays.asList(taskIpsAgentResultBean1);
        when(mockTaskIpsMapper.getTaskIpsInfo(any(Long[].class), eq(0L))).thenReturn(taskIpsAgentResultBeans1);

        // Configure TaskIpsMapper.getBindExecTaskIpsInfo(...).
        final TaskIpsAgentResultBean bindExecTaskIpsBean = new TaskIpsAgentResultBean();
        bindExecTaskIpsBean.setScriptTaskIpsId(0L);
        bindExecTaskIpsBean.setScriptAgentInfoId(0L);
        bindExecTaskIpsBean.setExecUserName("execUser");
        bindExecTaskIpsBean.setAgentIp("agentIp");
        bindExecTaskIpsBean.setAgentPort(0);
        final List<TaskIpsAgentResultBean> bindExecTaskIpsBeans = Arrays.asList(bindExecTaskIpsBean);
        when(mockTaskIpsMapper.getBindExecTaskIpsInfo(0L)).thenReturn(bindExecTaskIpsBeans);

        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("scriptType");

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure InfoVersionTextMapper.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(0L);
        infoVersionText.setCreatorName("creatorName");
        when(mockInfoVersionTextMapper.selectInfoVersionTextByScriptUuid("srcScriptUuid")).thenReturn(infoVersionText);

        // Configure TaskAttachmentMapper.selectTaskAttachmentList(...).
        final TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(0L);
        taskAttachment.setScriptTaskId(0L);
        taskAttachment.setName("name");
        taskAttachment.setSize(0L);
        taskAttachment.setContents("content".getBytes());
        final List<TaskAttachment> taskAttachments = Collections.singletonList(taskAttachment);
        when(mockTaskAttachmentMapper.selectTaskAttachmentList(any(TaskAttachment.class))).thenReturn(taskAttachments);



        // 模拟插入操作后生成的ID值
        final long instanceId = 12345L;
        doAnswer((Answer<Integer>) invocation -> {
            TaskInstanceDto taskInstanceDto = invocation.getArgument(0);
            taskInstanceDto.setId(instanceId);
            return 1; // 模拟插入操作成功返回1
        }).when(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class));

        // 创建spy对象来mock自引用方法
        TaskExecuteServiceImpl spyService = spy(taskExecuteServiceImplUnderTest);

        // 设置spy对象的自引用字段
        ReflectionTestUtils.setField(spyService, "iTaskExecuteService", spyService);

        // 模拟updateBatchStartAndCheck方法返回值
        StartAgentCommonParam mockStartAgentCommonParam = new StartAgentCommonParam();
        mockStartAgentCommonParam.setEachNum(2);
        mockStartAgentCommonParam.setTotal(1);
        mockStartAgentCommonParam.setTaskIpIds("0");
        mockStartAgentCommonParam.setParams("");
        mockStartAgentCommonParam.setScriptInParam("");
        doReturn(mockStartAgentCommonParam).when(spyService).updateBatchStartAndCheck(any(TaskStartDto.class));

        // 模拟批量插入方法
        doAnswer(invocation -> {
            // 获取传递给 batchData 方法的参数
            Class mapperClass = invocation.getArgument(0);
            List<TaskRuntime> taskRuntimeList = invocation.getArgument(1);
            Class entityClass = invocation.getArgument(2);
            String methodName = invocation.getArgument(3);
            SqlSession sqlSession = invocation.getArgument(4);
            TaskRuntimeMapper taskRuntimeMapper = invocation.getArgument(5);

            // 遍历 taskRuntimeList，并为每个 TaskRuntime 对象设置模拟的 ID 值
            for (TaskRuntime taskRuntime : taskRuntimeList) {
                long generatedId = 12345L; // 模拟生成的 ID 值
                taskRuntime.setId(generatedId);
            }

            // 返回模拟的结果，可以是 null 或者其他适当的返回值
            return null;
        }).when(mockBatchDataUtil).batchData(
                any(), // 模拟方法的第一个参数是 TaskRuntimeMapper 类
                any(), // 模拟方法的第二个参数是一个列表，类型为 TaskRuntime
                any(), // 模拟方法的第三个参数是 TaskRuntime 类
                any(), // 模拟方法的第四个参数是方法名字符串
                any(), // 模拟方法的第五个参数是 SqlSession 类
                any() // 模拟方法的第六个参数是 TaskRuntimeMapper 类
        );

        when(scriptBusinessConfig.isSendScriptParametersDesEnc()).thenReturn(false);
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptName("scriptName");
        when(mockInfoService.selectInfoByUniqueUuid(any())).thenReturn(scriptInfoDto);
        // Run the test
        final Long result = spyService.scriptTaskStart(taskStartDto, user);

        // Verify the results
        assertThat(result).isEqualTo(12345L);
        verify(mockTaskService, times(2)).updateTask(any(TaskDto.class));
        verify(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class));
        // verify(mockExectimeService).updateScriptExecTime(null, "srcScriptUuid", 3);

        // Confirm BatchDataUtil.batchData(...).
        final TaskRuntime taskRuntime = new TaskRuntime();
        taskRuntime.setBizId("bizId");
        taskRuntime.setScriptTaskIpsId(0L);
        taskRuntime.setAgentTaskId(0L);
        taskRuntime.setId(0L);
        taskRuntime.setScriptTaskId(0L);
        final List<TaskRuntime> listEntity = Collections.singletonList(taskRuntime);

        // Confirm AgentOperateApi.send(...).
        final AgentOperateDto agentOperateDto = new AgentOperateDto();
        agentOperateDto.setContent("");
        agentOperateDto.setContentFormatter("contentFormatter");
        agentOperateDto.setBizId("bizId");
        agentOperateDto.setTargetId(0L);
        agentOperateDto.setTargetType(TargetTypeEnum.AGENT);
        agentOperateDto.setRpcMethod("IEAIAgent.executeAct");
        final DynamicResourcesDto dynamicResourcesDTO = new DynamicResourcesDto();
        agentOperateDto.setDynamicResourcesDTO(dynamicResourcesDTO);
        final AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost("agentIp");
        agentOptionDto.setAgentPort(0L);
        agentOperateDto.setAgentOptionDto(agentOptionDto);
        final List<AgentOperateDto> agentOperateList = Collections.singletonList(agentOperateDto);
        // verify(mockAgentOperateApi).send(agentOperateList);
    }

    @Test
    void testUpdateBatchStartAndCheck() throws Exception {
        // Setup
        final TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setRetryRuntimeId(0L);
        taskStartDto.setDriveMode(0);
        taskStartDto.setScriptTaskId(0L);
        taskStartDto.setSrcScriptUuid("srcScriptUuid");
        taskStartDto.setTaskName("scriptName");
        taskStartDto.setInfoUniqueUuid("infoUniqueUuid");
        taskStartDto.setScriptType("sql");
        taskStartDto.setIscriptTaskInstanceId(0L);
        taskStartDto.setTaskIps(new Long[] { 0L });
        taskStartDto.setRetry(false);
        taskStartDto.setEachNum(2);

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType("type");
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Collections.singletonList(taskParams1);

        when(mockTaskIpsMapper.getTotalAgentCountForTask(0L)).thenReturn(0);

        when(mockTaskIpsMapper.getMaxOperIdForTask(0L)).thenReturn(0);

        // Run the test
        final StartAgentCommonParam result = taskExecuteServiceImplUnderTest.updateBatchStartAndCheck(taskStartDto);
        assertNotNull(result);
        // Verify the results
        verify(mockTaskIpsMapper).updateIpsStatusAndIncrementOperId(any(Long[].class), eq(1));
    }









    @Test
    void testScriptTaskStart_retry() throws Exception {
        // Setup
        String taskFlag = "script-task-code-flag-0";
        final TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setRetryRuntimeId(1L);
        taskStartDto.setDriveMode(0);
        taskStartDto.setScriptTaskId(0L);
        taskStartDto.setSrcScriptUuid("srcScriptUuid");
        taskStartDto.setTaskName("scriptName");
        taskStartDto.setInfoUniqueUuid("infoUniqueUuid");
        taskStartDto.setScriptType("scriptType");
        taskStartDto.setIscriptTaskInstanceId(0L);
        taskStartDto.setTaskIps(new Long[] { 0L });
        taskStartDto.setRetry(true);
        taskStartDto.setFirstBatch(true);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("loginName");

        // Configure ITaskService.selectTaskById(...).
        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(0);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(0L);
        taskDto.setSrcScriptUuid("srcScriptUuid");
        taskDto.setTaskName("scriptName");
        taskDto.setEachNum(0);
        taskDto.setTaskScheduler(0);
        taskDto.setTaskCron("taskCron");
        taskDto.setPublishDesc("publishDesc");
        taskDto.setTimeout(0L);
        taskDto.setType(0);
        taskDto.setDriveMode(0);
        when(mockTaskService.selectTaskById(0L)).thenReturn(taskDto);

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType("type");
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Collections.singletonList(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(taskParams);

        when(mockTaskIpsMapper.getTotalAgentCountForTask(0L)).thenReturn(0);

        when(mockTaskIpsMapper.getMaxOperIdForTask(0L)).thenReturn(0);

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setExpectType(0);

        infoVersionDto.setExpectLastline("expectLastline");
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(infoVersionDto);

        // Configure TaskIpsMapper.getTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean1 = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean1.setScriptTaskIpsId(0L);
        taskIpsAgentResultBean1.setScriptAgentInfoId(0L);
        taskIpsAgentResultBean1.setExecUserName("execUser");
        taskIpsAgentResultBean1.setAgentIp("agentIp");
        taskIpsAgentResultBean1.setAgentPort(0);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans1 = Arrays.asList(taskIpsAgentResultBean1);
        when(mockTaskIpsMapper.getTaskIpsInfo(any(Long[].class), eq(0L))).thenReturn(taskIpsAgentResultBeans1);

        // Configure TaskIpsMapper.getBindExecTaskIpsInfo(...).
        final TaskIpsAgentResultBean bindExecTaskIpsBean = new TaskIpsAgentResultBean();
        bindExecTaskIpsBean.setScriptTaskIpsId(0L);
        bindExecTaskIpsBean.setScriptAgentInfoId(0L);
        bindExecTaskIpsBean.setExecUserName("execUser");
        bindExecTaskIpsBean.setAgentIp("agentIp");
        bindExecTaskIpsBean.setAgentPort(0);
        final List<TaskIpsAgentResultBean> bindExecTaskIpsBeans = Arrays.asList(bindExecTaskIpsBean);
        when(mockTaskIpsMapper.getBindExecTaskIpsInfo(0L)).thenReturn(bindExecTaskIpsBeans);

        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("scriptType");

        final SqlSession mockSqlSession = mock(SqlSession.class);

        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(0L);
        infoVersionText.setCreatorName("creatorName");
        when(mockInfoVersionTextMapper.selectInfoVersionTextByScriptUuid("srcScriptUuid")).thenReturn(infoVersionText);

        // Configure TaskAttachmentMapper.selectTaskAttachmentList(...).
        final TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(0L);
        taskAttachment.setScriptTaskId(0L);
        taskAttachment.setName("name");
        taskAttachment.setSize(0L);
        taskAttachment.setContents("content".getBytes());
        final List<TaskAttachment> taskAttachments = Collections.singletonList(taskAttachment);
        when(mockTaskAttachmentMapper.selectTaskAttachmentList(any(TaskAttachment.class))).thenReturn(taskAttachments);

        when(mockRedissonClient.getAtomicLong(any(String.class))).thenReturn(mock(RAtomicLong.class));

        // 当调用 getBucket 方法时，返回模拟的 RBucket<String> 对象
        when(mockRedissonClient.getBucket(any(String.class))).thenReturn(mock(RBucket.class));

        // 模拟插入操作后生成的ID值
        final long instanceId = 12345L;
        doAnswer((Answer<Integer>) invocation -> {
            TaskInstanceDto taskInstanceDto = invocation.getArgument(0);
            taskInstanceDto.setId(instanceId);
            return 1; // 模拟插入操作成功返回1
        }).when(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class));

        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptName("scriptName");
        when(mockInfoService.selectInfoByUniqueUuid(any())).thenReturn(scriptInfoDto);
        RMap<?, ?> map = mock(RMap.class);

        when(mockRedissonClient.getMap(taskFlag)).thenAnswer((Answer<RMap<?, ?>>) invocation -> map);

        // Mock iTaskExecuteService.updateBatchStartAndCheck method
        StartAgentCommonParam mockStartAgentCommonParam = new StartAgentCommonParam();
        mockStartAgentCommonParam.setEachNum(0);
        mockStartAgentCommonParam.setTotal(1);
        mockStartAgentCommonParam.setParams("test-params");
        mockStartAgentCommonParam.setScriptInParam("test-script-in-param");
        mockStartAgentCommonParam.setTaskIpIds("0");
        when(iTaskExecuteService.updateBatchStartAndCheck(any(TaskStartDto.class))).thenReturn(mockStartAgentCommonParam);

        // Run the test
        final Long result = taskExecuteServiceImplUnderTest.scriptTaskStart(taskStartDto, user);

        // Verify the results
        assertNotNull(result);
        // verify(mockTaskIpsMapper).updateIpsStatusAndIncrementOperId(any(Long[].class), eq(1)); // Not called when updateBatchStartAndCheck is mocked
        // verify(mockTaskService, times(1)).updateTask(any(TaskDto.class)); // Not called when updateBatchStartAndCheck is mocked
        // verify(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class)); // Not called when updateBatchStartAndCheck is mocked
        // verify(mockExectimeService).updateScriptExecTime(null, "srcScriptUuid", 3);
        // verify(mockSqlSession).close();

        // Confirm BatchDataUtil.batchData(...).
        final TaskRuntime taskRuntime = new TaskRuntime();
        taskRuntime.setBizId("bizId");
        taskRuntime.setScriptTaskIpsId(0L);
        taskRuntime.setAgentTaskId(0L);
        taskRuntime.setId(0L);
        taskRuntime.setScriptTaskId(0L);
        final List<TaskRuntime> listEntity = Collections.singletonList(taskRuntime);

        verify(mockTaskRuntimeService).updateTaskRuntime(any(TaskRuntimeDto.class));

        // Confirm AgentOperateApi.send(...).
        final AgentOperateDto agentOperateDto = new AgentOperateDto();
        agentOperateDto.setContent("");
        agentOperateDto.setContentFormatter("contentFormatter");
        agentOperateDto.setBizId("bizId");
        agentOperateDto.setTargetId(0L);
        agentOperateDto.setTargetType(TargetTypeEnum.AGENT);
        agentOperateDto.setRpcMethod("IEAIAgent.executeAct");
        final DynamicResourcesDto dynamicResourcesDTO = new DynamicResourcesDto();
        agentOperateDto.setDynamicResourcesDTO(dynamicResourcesDTO);
        final AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost("agentIp");
        agentOptionDto.setAgentPort(0L);
        agentOperateDto.setAgentOptionDto(agentOptionDto);
        final List<AgentOperateDto> agentOperateList = Collections.singletonList(agentOperateDto);
        // verify(mockAgentOperateApi).send(agentOperateList);
    }

    @ParameterizedTest
    @CsvSource({
            "1", "2"
    })
    void testScriptTaskStart_TaskSource(int type) throws Exception {
        // Setup
        final TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setRetryRuntimeId(0L);
        taskStartDto.setDriveMode(0);
        taskStartDto.setScriptTaskId(0L);
        taskStartDto.setSrcScriptUuid("srcScriptUuid");
        taskStartDto.setTaskName("scriptName");
        taskStartDto.setInfoUniqueUuid("infoUniqueUuid");
        taskStartDto.setScriptType("scriptType");
        taskStartDto.setIscriptTaskInstanceId(0L);
        taskStartDto.setTaskIps(new Long[] { 0L });
        taskStartDto.setRetry(false);
        taskStartDto.setEachNum(1);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("loginName");

        // Configure ITaskService.selectTaskById(...).
        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(type);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(0L);
        taskDto.setSrcScriptUuid("srcScriptUuid");
        taskDto.setTaskName("scriptName");
        taskDto.setEachNum(0);
        taskDto.setTaskScheduler(0);
        taskDto.setTaskCron("taskCron");
        taskDto.setPublishDesc("publishDesc");
        taskDto.setTimeout(0L);
        taskDto.setType(0);
        taskDto.setDriveMode(0);
        when(mockTaskService.selectTaskById(0L)).thenReturn(taskDto);

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType("type");
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Collections.singletonList(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(taskParams);

        when(mockTaskIpsMapper.getTotalAgentCountForTask(0L)).thenReturn(0);

        when(mockTaskIpsMapper.getMaxOperIdForTask(0L)).thenReturn(0);

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setExpectType(0);

        infoVersionDto.setExpectLastline("expectLastline");
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(infoVersionDto);

        // Configure TaskIpsMapper.getTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean1 = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean1.setScriptTaskIpsId(0L);
        taskIpsAgentResultBean1.setScriptAgentInfoId(0L);
        taskIpsAgentResultBean1.setExecUserName("execUser");
        taskIpsAgentResultBean1.setAgentIp("agentIp");
        taskIpsAgentResultBean1.setAgentPort(0);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans1 = Arrays.asList(taskIpsAgentResultBean1);
        when(mockTaskIpsMapper.getTaskIpsInfo(any(Long[].class), eq(0L))).thenReturn(taskIpsAgentResultBeans1);

        // Configure TaskIpsMapper.getBindExecTaskIpsInfo(...).
        final TaskIpsAgentResultBean bindExecTaskIpsBean = new TaskIpsAgentResultBean();
        bindExecTaskIpsBean.setScriptTaskIpsId(0L);
        bindExecTaskIpsBean.setScriptAgentInfoId(0L);
        bindExecTaskIpsBean.setExecUserName("execUser");
        bindExecTaskIpsBean.setAgentIp("agentIp");
        bindExecTaskIpsBean.setAgentPort(0);
        final List<TaskIpsAgentResultBean> bindExecTaskIpsBeans = Arrays.asList(bindExecTaskIpsBean);
        when(mockTaskIpsMapper.getBindExecTaskIpsInfo(0L)).thenReturn(bindExecTaskIpsBeans);

        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("scriptType");

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure InfoVersionTextMapper.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(0L);
        infoVersionText.setCreatorName("creatorName");
        when(mockInfoVersionTextMapper.selectInfoVersionTextByScriptUuid("srcScriptUuid")).thenReturn(infoVersionText);

        // Configure TaskAttachmentMapper.selectTaskAttachmentList(...).
        final TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(0L);
        taskAttachment.setScriptTaskId(0L);
        taskAttachment.setName("name");
        taskAttachment.setSize(0L);
        taskAttachment.setContents("content".getBytes());
        final List<TaskAttachment> taskAttachments = Collections.singletonList(taskAttachment);
        when(mockTaskAttachmentMapper.selectTaskAttachmentList(any(TaskAttachment.class))).thenReturn(taskAttachments);

        when(mockRedissonClient.getAtomicLong(any(String.class))).thenReturn(mock(RAtomicLong.class));

        when(mockRedissonClient.getMap(anyString())).thenReturn(mock(RMap.class));

        // 当调用 getBucket 方法时，返回模拟的 RBucket<String> 对象
        when(mockRedissonClient.getBucket(any(String.class))).thenReturn(mock(RBucket.class));

        // 模拟插入操作后生成的ID值
        final long instanceId = 12345L;
        doAnswer((Answer<Integer>) invocation -> {
            TaskInstanceDto taskInstanceDto = invocation.getArgument(0);
            taskInstanceDto.setId(instanceId);
            return 1; // 模拟插入操作成功返回1
        }).when(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class));

        // 模拟批量插入方法
        doAnswer(invocation -> {
            // 获取传递给 batchData 方法的参数
            Class mapperClass = invocation.getArgument(0);
            List<TaskRuntime> taskRuntimeList = invocation.getArgument(1);
            Class entityClass = invocation.getArgument(2);
            String methodName = invocation.getArgument(3);
            SqlSession sqlSession = invocation.getArgument(4);
            TaskRuntimeMapper taskRuntimeMapper = invocation.getArgument(5);

            // 遍历 taskRuntimeList，并为每个 TaskRuntime 对象设置模拟的 ID 值
            for (TaskRuntime taskRuntime : taskRuntimeList) {
                long generatedId = 12345L; // 模拟生成的 ID 值
                taskRuntime.setId(generatedId);
            }

            // 返回模拟的结果，可以是 null 或者其他适当的返回值
            return null;
        }).when(mockBatchDataUtil).batchData(
                any(), // 模拟方法的第一个参数是 TaskRuntimeMapper 类
                any(), // 模拟方法的第二个参数是一个列表，类型为 TaskRuntime
                any(), // 模拟方法的第三个参数是 TaskRuntime 类
                any(), // 模拟方法的第四个参数是方法名字符串
                any(), // 模拟方法的第五个参数是 SqlSession 类
                any() // 模拟方法的第六个参数是 TaskRuntimeMapper 类
        );

        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptName("scriptName");
        when(mockInfoService.selectInfoByUniqueUuid(any())).thenReturn(scriptInfoDto);
        // Run the test - expect exception for this test case
        ScriptException exception = assertThrows(ScriptException.class, () -> {
            taskExecuteServiceImplUnderTest.scriptTaskStart(taskStartDto, user);
        });

        // Verify the exception message
        assertEquals("error.exec.script.task.start", exception.getMessage());

        // Note: Other verifications are not applicable when exception is thrown

        // Confirm BatchDataUtil.batchData(...).
        final TaskRuntime taskRuntime = new TaskRuntime();
        taskRuntime.setBizId("bizId");
        taskRuntime.setScriptTaskIpsId(0L);
        taskRuntime.setAgentTaskId(0L);
        taskRuntime.setId(0L);
        taskRuntime.setScriptTaskId(0L);
        final List<TaskRuntime> listEntity = Collections.singletonList(taskRuntime);

        // verify(mockTaskRuntimeService).updateTaskRuntime(any(TaskRuntimeDto.class)); // Not called when retry=false

        // Confirm AgentOperateApi.send(...).
        final AgentOperateDto agentOperateDto = new AgentOperateDto();
        agentOperateDto.setContent("");
        agentOperateDto.setContentFormatter("contentFormatter");
        agentOperateDto.setBizId("bizId");
        agentOperateDto.setTargetId(0L);
        agentOperateDto.setTargetType(TargetTypeEnum.AGENT);
        agentOperateDto.setRpcMethod("IEAIAgent.executeAct");
        final DynamicResourcesDto dynamicResourcesDTO = new DynamicResourcesDto();
        agentOperateDto.setDynamicResourcesDTO(dynamicResourcesDTO);
        final AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost("agentIp");
        agentOptionDto.setAgentPort(0L);
        agentOperateDto.setAgentOptionDto(agentOptionDto);
        final List<AgentOperateDto> agentOperateList = Collections.singletonList(agentOperateDto);
        // verify(mockAgentOperateApi).send(agentOperateList);
    }

    @ParameterizedTest
    @CsvSource({
            "IN-string", "IN-int", "IN-float", "OUT-string", "OUT-int", "OUT-float", "IN-type", "type-string"
    })
    void testScriptTaskStart_hasParam(String paramType) throws Exception {
        // Setup
        final TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setRetryRuntimeId(0L);
        taskStartDto.setDriveMode(0);
        taskStartDto.setScriptTaskId(0L);
        taskStartDto.setSrcScriptUuid("srcScriptUuid");
        taskStartDto.setTaskName("scriptName");
        taskStartDto.setInfoUniqueUuid("infoUniqueUuid");
        taskStartDto.setScriptType("sql");
        taskStartDto.setIscriptTaskInstanceId(0L);
        taskStartDto.setTaskIps(new Long[] { 0L });
        taskStartDto.setRetry(false);
        taskStartDto.setEachNum(1);
        taskStartDto.setFirstBatch(true);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("loginName");

        // Configure ITaskService.selectTaskById(...).
        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(0);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(0L);
        taskDto.setSrcScriptUuid("srcScriptUuid");
        taskDto.setTaskName("scriptName");
        taskDto.setEachNum(0);
        taskDto.setTaskScheduler(0);
        taskDto.setTaskCron("taskCron");
        taskDto.setPublishDesc("publishDesc");
        taskDto.setTimeout(0L);
        taskDto.setType(0);
        taskDto.setDriveMode(0);
        when(mockTaskService.selectTaskById(0L)).thenReturn(taskDto);

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType(paramType);
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Arrays.asList(taskParams1, taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(taskParams);

        when(mockTaskIpsMapper.getTotalAgentCountForTask(0L)).thenReturn(0);

        when(mockTaskIpsMapper.getMaxOperIdForTask(0L)).thenReturn(0);

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setExpectType(0);

        infoVersionDto.setExpectLastline("expectLastline");
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(infoVersionDto);

        // Configure TaskIpsMapper.getTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean1 = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean1.setScriptTaskIpsId(0L);
        taskIpsAgentResultBean1.setScriptAgentInfoId(0L);
        taskIpsAgentResultBean1.setExecUserName("execUser");
        taskIpsAgentResultBean1.setAgentIp("agentIp");
        taskIpsAgentResultBean1.setAgentPort(0);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans1 = Collections
                .singletonList(taskIpsAgentResultBean1);
        when(mockTaskIpsMapper.getTaskIpsInfo(any(Long[].class), eq(0L))).thenReturn(taskIpsAgentResultBeans1);

        // Configure TaskIpsMapper.getBindExecTaskIpsInfo(...).
        final TaskIpsAgentResultBean bindExecTaskIpsBean = new TaskIpsAgentResultBean();
        bindExecTaskIpsBean.setScriptTaskIpsId(0L);
        bindExecTaskIpsBean.setScriptAgentInfoId(0L);
        bindExecTaskIpsBean.setExecUserName("execUser");
        bindExecTaskIpsBean.setAgentIp("agentIp");
        bindExecTaskIpsBean.setAgentPort(0);
        final List<TaskIpsAgentResultBean> bindExecTaskIpsBeans = Arrays.asList(bindExecTaskIpsBean);
        when(mockTaskIpsMapper.getBindExecTaskIpsInfo(0L)).thenReturn(bindExecTaskIpsBeans);

        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("sql");

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure InfoVersionTextMapper.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(0L);
        infoVersionText.setCreatorName("creatorName");
        when(mockInfoVersionTextMapper.selectInfoVersionTextByScriptUuid("srcScriptUuid")).thenReturn(infoVersionText);

        // Configure TaskAttachmentMapper.selectTaskAttachmentList(...).
        final TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(0L);
        taskAttachment.setScriptTaskId(0L);
        taskAttachment.setName("name");
        taskAttachment.setSize(0L);
        taskAttachment.setContents("content".getBytes());
        final List<TaskAttachment> taskAttachments = Collections.singletonList(taskAttachment);
        when(mockTaskAttachmentMapper.selectTaskAttachmentList(any(TaskAttachment.class))).thenReturn(taskAttachments);

        when(mockRedissonClient.getAtomicLong(any(String.class))).thenReturn(mock(RAtomicLong.class));
        when(mockRedissonClient.getMap(anyString())).thenReturn(mock(RMap.class));

        // 当调用 getBucket 方法时，返回模拟的 RBucket<String> 对象
        when(mockRedissonClient.getBucket(any(String.class))).thenReturn(mock(RBucket.class));

        // 模拟插入操作后生成的ID值
        final long instanceId = 12345L;
        doAnswer((Answer<Integer>) invocation -> {
            TaskInstanceDto taskInstanceDto = invocation.getArgument(0);
            taskInstanceDto.setId(instanceId);
            return 1; // 模拟插入操作成功返回1
        }).when(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class));

        // 模拟批量插入方法
        doAnswer(invocation -> {
            // 获取传递给 batchData 方法的参数
            Class mapperClass = invocation.getArgument(0);
            List<TaskRuntime> taskRuntimeList = invocation.getArgument(1);
            Class entityClass = invocation.getArgument(2);
            String methodName = invocation.getArgument(3);
            SqlSession sqlSession = invocation.getArgument(4);
            TaskRuntimeMapper taskRuntimeMapper = invocation.getArgument(5);

            // 遍历 taskRuntimeList，并为每个 TaskRuntime 对象设置模拟的 ID 值
            for (TaskRuntime taskRuntime : taskRuntimeList) {
                long generatedId = 12345L; // 模拟生成的 ID 值
                taskRuntime.setId(generatedId);
            }

            // 返回模拟的结果，可以是 null 或者其他适当的返回值
            return null;
        }).when(mockBatchDataUtil).batchData(
                any(), // 模拟方法的第一个参数是 TaskRuntimeMapper 类
                any(), // 模拟方法的第二个参数是一个列表，类型为 TaskRuntime
                any(), // 模拟方法的第三个参数是 TaskRuntime 类
                any(), // 模拟方法的第四个参数是方法名字符串
                any(), // 模拟方法的第五个参数是 SqlSession 类
                any() // 模拟方法的第六个参数是 TaskRuntimeMapper 类
        );

        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptName("scriptName");
        when(mockInfoService.selectInfoByUniqueUuid(any())).thenReturn(scriptInfoDto);

        // Mock iTaskExecuteService.updateBatchStartAndCheck method
        StartAgentCommonParam mockStartAgentCommonParam = new StartAgentCommonParam();
        mockStartAgentCommonParam.setEachNum(0);
        mockStartAgentCommonParam.setTotal(1);
        mockStartAgentCommonParam.setParams("test-params");
        mockStartAgentCommonParam.setScriptInParam("test-script-in-param");
        mockStartAgentCommonParam.setTaskIpIds("0");
        when(iTaskExecuteService.updateBatchStartAndCheck(any(TaskStartDto.class))).thenReturn(mockStartAgentCommonParam);

        // Run the test
        final Long result = taskExecuteServiceImplUnderTest.scriptTaskStart(taskStartDto, user);

        // Verify the results
        assertNotNull(result);
        // verify(mockTaskIpsMapper).updateIpsStatusAndIncrementOperId(any(Long[].class), eq(1)); // Not called when updateBatchStartAndCheck is mocked
        // verify(mockTaskService, times(2)).updateTask(any(TaskDto.class)); // Not called when updateBatchStartAndCheck is mocked
        // verify(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class)); // Not called when updateBatchStartAndCheck is mocked
        // verify(mockExectimeService).updateScriptExecTime(null, "srcScriptUuid", 3);
        // verify(mockSqlSession).close(); // Not called when updateBatchStartAndCheck is mocked

        // Confirm BatchDataUtil.batchData(...).
        final TaskRuntime taskRuntime = new TaskRuntime();
        taskRuntime.setBizId("bizId");
        taskRuntime.setScriptTaskIpsId(0L);
        taskRuntime.setAgentTaskId(0L);
        taskRuntime.setId(0L);
        taskRuntime.setScriptTaskId(0L);
        final List<TaskRuntime> listEntity = Collections.singletonList(taskRuntime);

        // verify(mockTaskRuntimeService).updateTaskRuntime(any(TaskRuntimeDto.class)); // Not called when updateBatchStartAndCheck is mocked

        // Confirm AgentOperateApi.send(...).
        final AgentOperateDto agentOperateDto = new AgentOperateDto();
        agentOperateDto.setContent("");
        agentOperateDto.setContentFormatter("contentFormatter");
        agentOperateDto.setBizId("bizId");
        agentOperateDto.setTargetId(0L);
        agentOperateDto.setTargetType(TargetTypeEnum.AGENT);
        agentOperateDto.setRpcMethod("IEAIAgent.executeAct");
        final DynamicResourcesDto dynamicResourcesDTO = new DynamicResourcesDto();
        agentOperateDto.setDynamicResourcesDTO(dynamicResourcesDTO);
        final AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost("agentIp");
        agentOptionDto.setAgentPort(0L);
        agentOperateDto.setAgentOptionDto(agentOptionDto);
        final List<AgentOperateDto> agentOperateList = Collections.singletonList(agentOperateDto);
        // verify(mockAgentOperateApi).send(agentOperateList);
    }

    @ParameterizedTest
    @CsvSource({
            "IN-string,1,1", "IN-string,2,1", "IN-string,1,2", "IN-string,2,2"
    })
    void testScriptTaskStart_hasLastLine(String paramType, Integer lastLine, Integer expectCategory) throws Exception {
        // Setup
        final TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setRetryRuntimeId(0L);
        taskStartDto.setDriveMode(0);
        taskStartDto.setScriptTaskId(0L);
        taskStartDto.setSrcScriptUuid("srcScriptUuid");
        taskStartDto.setTaskName("scriptName");
        taskStartDto.setInfoUniqueUuid("infoUniqueUuid");
        taskStartDto.setScriptType("sql");
        taskStartDto.setIscriptTaskInstanceId(0L);
        taskStartDto.setTaskIps(new Long[] { 0L });
        taskStartDto.setRetry(false);
        taskStartDto.setEachNum(1);
        taskStartDto.setFirstBatch(true);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("loginName");

        // Configure ITaskService.selectTaskById(...).
        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(0);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(0L);
        taskDto.setSrcScriptUuid("srcScriptUuid");
        taskDto.setTaskName("scriptName");
        taskDto.setEachNum(0);
        taskDto.setTaskScheduler(0);
        taskDto.setTaskCron("taskCron");
        taskDto.setPublishDesc("publishDesc");
        taskDto.setTimeout(0L);
        taskDto.setType(0);
        taskDto.setDriveMode(0);
        when(mockTaskService.selectTaskById(0L)).thenReturn(taskDto);

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType(paramType);
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Collections.singletonList(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(taskParams);

        when(mockTaskIpsMapper.getTotalAgentCountForTask(0L)).thenReturn(0);

        when(mockTaskIpsMapper.getMaxOperIdForTask(0L)).thenReturn(0);

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setExpectType(lastLine);
        infoVersionDto.setExpectLastline("expectLastline");
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(infoVersionDto);

        // Configure TaskIpsMapper.getTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean1 = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean1.setScriptTaskIpsId(0L);
        taskIpsAgentResultBean1.setScriptAgentInfoId(0L);
        taskIpsAgentResultBean1.setExecUserName("execUser");
        taskIpsAgentResultBean1.setAgentIp("agentIp");
        taskIpsAgentResultBean1.setAgentPort(0);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans1 = Collections
                .singletonList(taskIpsAgentResultBean1);
        when(mockTaskIpsMapper.getTaskIpsInfo(any(Long[].class), eq(0L))).thenReturn(taskIpsAgentResultBeans1);

        // Configure TaskIpsMapper.getBindExecTaskIpsInfo(...).
        final TaskIpsAgentResultBean bindExecTaskIpsBean = new TaskIpsAgentResultBean();
        bindExecTaskIpsBean.setScriptTaskIpsId(0L);
        bindExecTaskIpsBean.setScriptAgentInfoId(0L);
        bindExecTaskIpsBean.setExecUserName("execUser");
        bindExecTaskIpsBean.setAgentIp("agentIp");
        bindExecTaskIpsBean.setAgentPort(0);
        final List<TaskIpsAgentResultBean> bindExecTaskIpsBeans = Arrays.asList(bindExecTaskIpsBean);
        when(mockTaskIpsMapper.getBindExecTaskIpsInfo(0L)).thenReturn(bindExecTaskIpsBeans);

        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("sql");

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure InfoVersionTextMapper.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(0L);
        infoVersionText.setCreatorName("creatorName");
        when(mockInfoVersionTextMapper.selectInfoVersionTextByScriptUuid("srcScriptUuid")).thenReturn(infoVersionText);

        // Configure TaskAttachmentMapper.selectTaskAttachmentList(...).
        final TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(0L);
        taskAttachment.setScriptTaskId(0L);
        taskAttachment.setName("name");
        taskAttachment.setSize(0L);
        taskAttachment.setContents("content".getBytes());
        final List<TaskAttachment> taskAttachments = Collections.singletonList(taskAttachment);
        when(mockTaskAttachmentMapper.selectTaskAttachmentList(any(TaskAttachment.class))).thenReturn(taskAttachments);

        when(mockRedissonClient.getAtomicLong(any(String.class))).thenReturn(mock(RAtomicLong.class));
        when(mockRedissonClient.getMap(anyString())).thenReturn(mock(RMap.class));

        // 当调用 getBucket 方法时，返回模拟的 RBucket<String> 对象
        when(mockRedissonClient.getBucket(any(String.class))).thenReturn(mock(RBucket.class));

        // 模拟插入操作后生成的ID值
        final long instanceId = 12345L;
        doAnswer((Answer<Integer>) invocation -> {
            TaskInstanceDto taskInstanceDto = invocation.getArgument(0);
            taskInstanceDto.setId(instanceId);
            return 1; // 模拟插入操作成功返回1
        }).when(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class));

        // 模拟批量插入方法
        doAnswer(invocation -> {
            // 获取传递给 batchData 方法的参数
            Class mapperClass = invocation.getArgument(0);
            List<TaskRuntime> taskRuntimeList = invocation.getArgument(1);
            Class entityClass = invocation.getArgument(2);
            String methodName = invocation.getArgument(3);
            SqlSession sqlSession = invocation.getArgument(4);
            TaskRuntimeMapper taskRuntimeMapper = invocation.getArgument(5);

            // 遍历 taskRuntimeList，并为每个 TaskRuntime 对象设置模拟的 ID 值
            for (TaskRuntime taskRuntime : taskRuntimeList) {
                long generatedId = 12345L; // 模拟生成的 ID 值
                taskRuntime.setId(generatedId);
            }

            // 返回模拟的结果，可以是 null 或者其他适当的返回值
            return null;
        }).when(mockBatchDataUtil).batchData(
                any(), // 模拟方法的第一个参数是 TaskRuntimeMapper 类
                any(), // 模拟方法的第二个参数是一个列表，类型为 TaskRuntime
                any(), // 模拟方法的第三个参数是 TaskRuntime 类
                any(), // 模拟方法的第四个参数是方法名字符串
                any(), // 模拟方法的第五个参数是 SqlSession 类
                any() // 模拟方法的第六个参数是 TaskRuntimeMapper 类
        );

        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptName("scriptName");
        when(mockInfoService.selectInfoByUniqueUuid(any())).thenReturn(scriptInfoDto);

        // Mock iTaskExecuteService.updateBatchStartAndCheck method
        StartAgentCommonParam mockStartAgentCommonParam = new StartAgentCommonParam();
        mockStartAgentCommonParam.setEachNum(0);
        mockStartAgentCommonParam.setTotal(1);
        mockStartAgentCommonParam.setParams("test-params");
        mockStartAgentCommonParam.setScriptInParam("test-script-in-param");
        mockStartAgentCommonParam.setTaskIpIds("0");
        when(iTaskExecuteService.updateBatchStartAndCheck(any(TaskStartDto.class))).thenReturn(mockStartAgentCommonParam);

        // Run the test
        final Long result = taskExecuteServiceImplUnderTest.scriptTaskStart(taskStartDto, user);

        // Verify the results
        assertNotNull(result);
        // verify(mockTaskIpsMapper).updateIpsStatusAndIncrementOperId(any(Long[].class), eq(1)); // Not called when updateBatchStartAndCheck is mocked
        // verify(mockTaskService, times(2)).updateTask(any(TaskDto.class)); // Not called when updateBatchStartAndCheck is mocked
        // verify(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class)); // Not called when updateBatchStartAndCheck is mocked
        // verify(mockExectimeService).updateScriptExecTime(null, "srcScriptUuid", 3);
        // verify(mockSqlSession).close(); // Not called when updateBatchStartAndCheck is mocked

        // Confirm BatchDataUtil.batchData(...).
        final TaskRuntime taskRuntime = new TaskRuntime();
        taskRuntime.setBizId("bizId");
        taskRuntime.setScriptTaskIpsId(0L);
        taskRuntime.setAgentTaskId(0L);
        taskRuntime.setId(0L);
        taskRuntime.setScriptTaskId(0L);
        final List<TaskRuntime> listEntity = Collections.singletonList(taskRuntime);

        // verify(mockTaskRuntimeService).updateTaskRuntime(any(TaskRuntimeDto.class)); // Not called when updateBatchStartAndCheck is mocked

        // Confirm AgentOperateApi.send(...).
        final AgentOperateDto agentOperateDto = new AgentOperateDto();
        agentOperateDto.setContent("");
        agentOperateDto.setContentFormatter("contentFormatter");
        agentOperateDto.setBizId("bizId");
        agentOperateDto.setTargetId(0L);
        agentOperateDto.setTargetType(TargetTypeEnum.AGENT);
        agentOperateDto.setRpcMethod("IEAIAgent.executeAct");
        final DynamicResourcesDto dynamicResourcesDTO = new DynamicResourcesDto();
        agentOperateDto.setDynamicResourcesDTO(dynamicResourcesDTO);
        final AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost("agentIp");
        agentOptionDto.setAgentPort(0L);
        agentOperateDto.setAgentOptionDto(agentOptionDto);
        final List<AgentOperateDto> agentOperateList = Collections.singletonList(agentOperateDto);
        // verify(mockAgentOperateApi).send(agentOperateList);
    }

    @Test
    void testListRunningScriptTasks() {
        // Setup
        final TaskExecuteQueryDto taskExecuteDto = new TaskExecuteQueryDto();
        taskExecuteDto.setTaskName("taskName");
        taskExecuteDto.setCategoryId(0L);

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");

        // Configure ITaskService.selectRunningScriptTasks(...).
        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean.setCategoryId(0L);
        Page<TaskExecuteBean> page = new Page<>();
        page.add(taskExecuteBean);
        when(mockTaskService.selectRunningScriptTasks(any(TaskExecuteBean.class), any(CurrentUser.class)))
                .thenReturn(page);

        when(mockCategoryService.getCategoryFullPath(0L)).thenReturn("scriptCategoryName");

        // Run the test
        final PageInfo<TaskExecuteDto> result = taskExecuteServiceImplUnderTest.listRunningScriptTasks(taskExecuteDto,
                0, 0, currentUser);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testListCompleteScriptTasks() {
        // Setup
        final TaskExecuteQueryDto taskExecuteQueryDto = new TaskExecuteQueryDto();
        taskExecuteQueryDto.setTaskName("taskName");
        taskExecuteQueryDto.setCategoryId(0L);

        final CurrentUser currentUser = new CurrentUser();
        currentUser.setId(0L);
        currentUser.setLoginName("loginName");

        // Configure ITaskService.selectCompleteScriptTasks(...).
        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean.setCategoryId(0L);
        Page<TaskExecuteBean> page = new Page<>();
        page.add(taskExecuteBean);
        when(mockTaskService.selectCompleteScriptTasks(any(TaskExecuteBean.class), any(CurrentUser.class)))
                .thenReturn(page);

        when(mockCategoryService.getCategoryFullPath(0L)).thenReturn("scriptCategoryName");

        // Run the test
        final PageInfo<TaskExecuteDto> result = taskExecuteServiceImplUnderTest.listCompleteScriptTasks(
                taskExecuteQueryDto, 0, 0, currentUser);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testScriptTestExecution() throws Exception {
        // Setup
        final ScriptTestExecutionDto testExecutionDto = new ScriptTestExecutionDto();
        final TaskParamsDto taskParamsDto = new TaskParamsDto();
        taskParamsDto.setId(0L);
        taskParamsDto.setScriptTaskId(0L);
        taskParamsDto.setStartType(0);
        testExecutionDto.setParams(Arrays.asList(taskParamsDto));
        testExecutionDto.setSrcScriptUuid("srcScriptUuid");
        testExecutionDto.setInfoId(0L);
        testExecutionDto.setExecuser("ideal");
        testExecutionDto.setResGroupFlag(true);
        final AttachmentDto attachmentDto = new AttachmentDto();
        testExecutionDto.setScriptTempAttachments(Arrays.asList(attachmentDto));
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        testExecutionDto.setChosedAgentUsers(Arrays.asList(agentInfoDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        testExecutionDto.setChosedResGroups(Arrays.asList(taskGroupsDto));

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("loginName");

        // Configure IInfoService.selectInfoById(...).
        final ScriptInfoDto infoDto = new ScriptInfoDto();
        final Category category = new Category();
        final CategoryApiDto categoryApiDto = new CategoryApiDto();
        categoryApiDto.setChildren(Arrays.asList(new CategoryApiDto()));
        category.setChildren(Arrays.asList(categoryApiDto));
        infoDto.setCategoryDto(new CategoryDto());
        infoDto.setId(1L);
        infoDto.setScriptName("scriptName");
        when(mockInfoService.selectInfoById(0L)).thenReturn(infoDto);

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure ITaskGroupsService.retrieveUniqueAgentInfoList(...).
        final AgentInfoDto agentInfoDto1 = new AgentInfoDto();
        agentInfoDto1.setId(0L);
        agentInfoDto1.setSysmAgentInfoId(0L);
        agentInfoDto1.setAgentIp("agentIp");
        agentInfoDto1.setAgentName("agentName");
        agentInfoDto1.setAgentPort(0);
        final List<AgentInfoDto> agentInfoDtoList = Arrays.asList(agentInfoDto1);
        final TaskGroupsDto taskGroupsDto1 = new TaskGroupsDto();
        taskGroupsDto1.setId(0L);
        taskGroupsDto1.setScriptTaskId(0L);
        taskGroupsDto1.setSysmComputerGroupId(0L);
        taskGroupsDto1.setCpname("cpname");
        taskGroupsDto1.setCreateTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        final List<TaskGroupsDto> taskGroupsDtoList = Arrays.asList(taskGroupsDto1);
        when(mockTaskGroupsService.retrieveUniqueAgentInfoList(any(List.class))).thenReturn(agentInfoDtoList);

        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("scriptType");

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setExpectType(0);

        infoVersionDto.setExpectLastline("expectLastline");
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(infoVersionDto);

        // Configure TaskIpsMapper.getBindAllTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean.setScriptTaskIpsId(0L);
        taskIpsAgentResultBean.setScriptAgentInfoId(0L);
        taskIpsAgentResultBean.setExecUserName("execUser");
        taskIpsAgentResultBean.setAgentIp("agentIp");
        taskIpsAgentResultBean.setAgentPort(0);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans = Arrays.asList(taskIpsAgentResultBean);
        when(mockTaskIpsMapper.getBindAllTaskIpsInfo(any(Long.class))).thenReturn(taskIpsAgentResultBeans);

        // Configure ITaskService.selectTaskById(...).
        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(0);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(0L);
        taskDto.setSrcScriptUuid("srcScriptUuid");
        taskDto.setTaskName("scriptName");
        taskDto.setEachNum(0);
        taskDto.setTaskScheduler(0);
        taskDto.setTaskCron("taskCron");
        taskDto.setPublishDesc("publishDesc");
        taskDto.setTimeout(0L);
        taskDto.setType(0);
        taskDto.setDriveMode(0);
        when(mockTaskService.selectTaskById(any(Long.class))).thenReturn(taskDto);

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType("type");
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Arrays.asList(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(taskParams);

        when(mockTaskIpsMapper.getTotalAgentCountForTask(any(Long.class))).thenReturn(0);

        when(mockTaskIpsMapper.getMaxOperIdForTask(any(Long.class))).thenReturn(0);

        // Configure TaskIpsMapper.getTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean1 = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean1.setScriptTaskIpsId(0L);
        taskIpsAgentResultBean1.setScriptAgentInfoId(0L);
        taskIpsAgentResultBean1.setExecUserName("execUser");
        taskIpsAgentResultBean1.setAgentIp("agentIp");
        taskIpsAgentResultBean1.setAgentPort(0);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans1 = Arrays.asList(taskIpsAgentResultBean1);
        when(mockTaskIpsMapper.getTaskIpsInfo(any(Long[].class), eq(12345L))).thenReturn(taskIpsAgentResultBeans1);

        // Configure InfoVersionTextMapper.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(0L);
        infoVersionText.setCreatorName("creatorName");
        when(mockInfoVersionTextMapper.selectInfoVersionTextByScriptUuid("srcScriptUuid")).thenReturn(infoVersionText);

        // Configure TaskAttachmentMapper.selectTaskAttachmentList(...).
        final TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(0L);
        taskAttachment.setScriptTaskId(0L);
        taskAttachment.setName("name");
        taskAttachment.setSize(0L);
        taskAttachment.setContents("content".getBytes());
        final List<TaskAttachment> taskAttachments = Arrays.asList(taskAttachment);
        when(mockTaskAttachmentMapper.selectTaskAttachmentList(any(TaskAttachment.class))).thenReturn(taskAttachments);
        when(mockRedissonClient.getAtomicLong(any(String.class))).thenReturn(mock(RAtomicLong.class));
        when(mockRedissonClient.getMap(anyString())).thenReturn(mock(RMap.class));

        // 当调用 getBucket 方法时，返回模拟的 RBucket<String> 对象
        when(mockRedissonClient.getBucket(any(String.class))).thenReturn(mock(RBucket.class));
        // 模拟插入操作后生成的ID值
        final long taskInfoId = 12345L;
        doAnswer((Answer<Integer>) invocation -> {
            TaskDto taskDto1 = invocation.getArgument(0);
            taskDto1.setId(taskInfoId);
            return 1; // 模拟插入操作成功返回1
        }).when(mockTaskService).insertTask(any(TaskDto.class));

        // 仅模拟特定的方法调用
        doAnswer(invocation -> {
            // 获取传递给 batchData 方法的参数
            Class mapperClass = invocation.getArgument(0);
            List<TaskRuntime> taskRuntimeList = invocation.getArgument(1);
            Class entityClass = invocation.getArgument(2);
            String methodName = invocation.getArgument(3);
            SqlSession sqlSession = invocation.getArgument(4);
            TaskRuntimeMapper taskRuntimeMapper = invocation.getArgument(5);

            // 检查方法名是否是第一个批量插入的方法名
            if ("insertTaskRuntime".equals(methodName)) {
                // 遍历 taskRuntimeList，并为每个 TaskRuntime 对象设置模拟的 ID 值
                for (TaskRuntime taskRuntime : taskRuntimeList) {
                    long generatedId = 12345L; // 模拟生成的 ID 值
                    taskRuntime.setId(generatedId);
                }
            }

            // 返回模拟的结果，可以是 null 或者其他适当的返回值
            return null;
        }).when(mockBatchDataUtil).batchData(
                any(), // 模拟方法的第一个参数是 TaskRuntimeMapper 类
                any(), // 模拟方法的第二个参数是一个列表，类型为 TaskRuntime
                any(), // 模拟方法的第三个参数是 TaskRuntime 类
                any(), // 模拟方法的第四个参数是方法名字符串，这里使用了anyString()来匹配任何方法名
                any(), // 模拟方法的第五个参数是 SqlSession 类
                any() // 模拟方法的第六个参数是 TaskRuntimeMapper 类
        );

        // 模拟插入操作后生成的ID值
        final long taskInstanceId = 12345L;
        doAnswer((Answer<Integer>) invocation -> {
            TaskInstanceDto taskInstanceDto = invocation.getArgument(0);
            taskInstanceDto.setId(taskInstanceId);
            return 1; // 模拟插入操作成功返回1
        }).when(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class));

        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptName("scriptName");
        when(mockInfoService.selectInfoByUniqueUuid(any())).thenReturn(scriptInfoDto);
        // Run the test
        final Long result = taskExecuteServiceImplUnderTest.scriptTestExecution(testExecutionDto, user);

        // Verify the results
        assertNotNull(result);
        verify(mockTaskService).insertTask(any(TaskDto.class));
        // verify(mockSqlSession, times(2)).close();
        verify(mockTaskAttachmentService).saveTaskAttachement(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskGroupsService).saveTaskGroups(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        verify(mockTaskIpsService).saveTaskIps(any(ScriptExecAuditDto.class), any(TaskDto.class),
                any(SqlSession.class));
        // verify(mockTaskIpsMapper).updateIpsStatusAndIncrementOperId(any(Long[].class),
        // eq(1));

        // Confirm BatchDataUtil.batchData(...).
        final TaskRuntime taskRuntime = new TaskRuntime();
        taskRuntime.setBizId("bizId");
        taskRuntime.setScriptTaskIpsId(0L);
        taskRuntime.setAgentTaskId(0L);
        taskRuntime.setId(0L);
        taskRuntime.setScriptTaskId(0L);
        final List<TaskRuntime> listEntity = Collections.singletonList(taskRuntime);

        // Confirm AgentOperateApi.send(...).
        final AgentOperateDto agentOperateDto = new AgentOperateDto();
        agentOperateDto.setContent("");
        agentOperateDto.setContentFormatter("contentFormatter");
        agentOperateDto.setBizId("bizId");
        agentOperateDto.setTargetId(0L);
        agentOperateDto.setTargetType(TargetTypeEnum.AGENT);
        agentOperateDto.setRpcMethod("IEAIAgent.executeAct");
        final DynamicResourcesDto dynamicResourcesDTO = new DynamicResourcesDto();
        agentOperateDto.setDynamicResourcesDTO(dynamicResourcesDTO);
        final AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost("agentIp");
        agentOptionDto.setAgentPort(0L);
        agentOperateDto.setAgentOptionDto(agentOptionDto);
        final List<AgentOperateDto> agentOperateList = Arrays.asList(agentOperateDto);
        // verify(mockAgentOperateApi).send(agentOperateList);
    }

    @Test
    void testRetryScriptServiceShell() throws Exception {
        // Setup
        final CurrentUser user = new CurrentUser();
        user.setId(1L);
        user.setLoginName("loginName");

        when(mockTaskRuntimeService.selectCountByTaskInstanceId(1L)).thenReturn(1);

        // Configure ITaskRuntimeService.selectTaskRuntimeById(...).
        String taskFlag = "script-task-code-flag-1";
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setScriptTaskIpsId(1L);
        taskRuntimeDto.setId(1L);
        taskRuntimeDto.setScriptTaskId(1L);
        taskRuntimeDto.setTaskInstanceId(1L);
        taskRuntimeDto.setSrcScriptUuid("srcScriptUuid");
        taskRuntimeDto.setState(0);
        taskRuntimeDto.setScriptName("scriptName");
        taskRuntimeDto.setAgentIp("agentIp");
        taskRuntimeDto.setExecUser("execUser");
        taskRuntimeDto.setBizId("bizId");
        taskRuntimeDto.setStartTime(Timestamp.valueOf(LocalDateTime.of(2025, 1, 1, 0, 0, 0, 0)));
        taskRuntimeDto.setExpectLastline("expectLastline");
        taskRuntimeDto.setTimeoutValue(0L);
        taskRuntimeDto.setAgentPort(15000);
        taskRuntimeDto.setExpectType(0);
        taskRuntimeDto.setStartType(0);
        when(mockTaskRuntimeService.selectTaskRuntimeById(1L)).thenReturn(taskRuntimeDto);

        // Configure ITaskService.selectTaskById(...).
        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(0);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(1L);
        taskDto.setSrcScriptUuid("srcScriptUuid");
        taskDto.setTaskName("scriptName");
        taskDto.setEachNum(0);
        taskDto.setTaskScheduler(0);
        taskDto.setTaskCron("taskCron");
        taskDto.setPublishDesc("publishDesc");
        taskDto.setTimeout(0L);
        taskDto.setType(0);
        taskDto.setDriveMode(0);
        when(mockTaskService.selectTaskById(1L)).thenReturn(taskDto);

        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("scriptType");

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(1L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setExpectType(0);
        infoVersionDto.setExpectLastline("expectLastline");
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(infoVersionDto);

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(1L);
        taskParams1.setScriptTaskId(1L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType("type");
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Arrays.asList(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(taskParams);

        when(mockTaskIpsMapper.getTotalAgentCountForTask(1L)).thenReturn(0);

        when(mockTaskIpsMapper.getMaxOperIdForTask(1L)).thenReturn(0);

        // Configure TaskIpsMapper.getBindAllTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean.setScriptTaskIpsId(0L);
        taskIpsAgentResultBean.setScriptAgentInfoId(0L);
        taskIpsAgentResultBean.setExecUserName("execUser");
        taskIpsAgentResultBean.setAgentIp("agentIp");
        taskIpsAgentResultBean.setAgentPort(0);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans = Arrays.asList(taskIpsAgentResultBean);
        // when(mockTaskIpsMapper.getBindAllTaskIpsInfo(anyLong())).thenReturn(taskIpsAgentResultBeans);

        // Configure TaskIpsMapper.getTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean1 = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean1.setScriptTaskIpsId(1L);
        taskIpsAgentResultBean1.setScriptAgentInfoId(1L);
        taskIpsAgentResultBean1.setExecUserName("execUser");
        taskIpsAgentResultBean1.setAgentIp("agentIp");
        taskIpsAgentResultBean1.setAgentPort(15000);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans1 = Arrays.asList(taskIpsAgentResultBean1);
        when(mockTaskIpsMapper.getTaskIpsInfo(any(Long[].class), eq(1L))).thenReturn(taskIpsAgentResultBeans1);

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        // when(mockFactory.openSession(ExecutorType.BATCH,
        // false)).thenReturn(mockSqlSession);

        // Configure InfoVersionTextMapper.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(1L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(0L);
        infoVersionText.setCreatorName("creatorName");
        when(mockInfoVersionTextMapper.selectInfoVersionTextByScriptUuid("srcScriptUuid")).thenReturn(infoVersionText);

        // Configure TaskAttachmentMapper.selectTaskAttachmentList(...).
        final TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(1L);
        taskAttachment.setScriptTaskId(1L);
        taskAttachment.setName("name");
        taskAttachment.setSize(1L);
        taskAttachment.setContents("content".getBytes());
        final List<TaskAttachment> taskAttachments = Arrays.asList(taskAttachment);
        when(mockTaskAttachmentMapper.selectTaskAttachmentList(any(TaskAttachment.class))).thenReturn(taskAttachments);

        when(mockRedissonClient.getAtomicLong(any(String.class))).thenReturn(mock(RAtomicLong.class));

        // 当调用 getBucket 方法时，返回模拟的 RBucket<String> 对象
        when(mockRedissonClient.getBucket(any(String.class))).thenReturn(mock(RBucket.class));
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptName("scriptName");
        when(mockInfoService.selectInfoByUniqueUuid(any())).thenReturn(scriptInfoDto);
        RMap<?, ?> map = mock(RMap.class);

        when(mockRedissonClient.getMap(taskFlag)).thenAnswer((Answer<RMap<?, ?>>) invocation -> map);

        // Run the test
        taskExecuteServiceImplUnderTest.retryScriptServiceShell(1L, 1L, user);

        // Verify the results
        verify(mockTaskRuntimeService, times(1)).updateTaskRuntime(any(TaskRuntimeDto.class));
        verify(mockTaskInstanceService).updateTaskInstance(any(TaskInstanceDto.class));
        // verify(mockTaskIpsMapper).updateIpsStatusAndIncrementOperId(any(Long[].class),
        // eq(1));
        // verify(mockTaskService,times(1)).updateTask(any(TaskDto.class));
        // verify(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class));
        // verify(mockExectimeService).updateScriptExecTime(null, "srcScriptUuid", 3);
        // verify(mockSqlSession).close();

    }

    @Test
    void testBuildVector() {
        // Setup
        final StartAgentParams startAgentParams = new StartAgentParams();
        startAgentParams.setScriptInParam("scriptInParam");
        startAgentParams.setScriptType("scriptType");
        startAgentParams.setScriptAgentInfoId(0L);
        startAgentParams.setScriptTaskIpsId(0L);
        startAgentParams.setScriptWorkDir("scriptWorkDir");
        startAgentParams.setScriptHashMap(new HashMap<>());
        startAgentParams.setBizId("bizId");
        startAgentParams.setDsId(0L);
        startAgentParams.setScriptTaskId(0L);
        startAgentParams.setScriptEnvironment("scriptEnvironment");
        startAgentParams.setSrcScriptUuid("srcScriptUuid");
        startAgentParams.setInfoUniqueUuid("infoUniqueUuid");
        startAgentParams.setAgentIp("agentIp");
        startAgentParams.setAgentPort(0);
        startAgentParams.setUserId("userId");
        startAgentParams.setAgentParam("agentParam");
        startAgentParams.setTaskRuntimeId(0L);
        startAgentParams.setExpectLastLine("expectLastline");
        startAgentParams.setErrorExpectLastLine("errorExpectLastLine");
        startAgentParams.setExpectType(0);
        startAgentParams.setExecUserName("execUser");

        // Run the test
        final List<Object> result = taskExecuteServiceImplUnderTest.buildVector(startAgentParams);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testOrganizeScriptHashtable() {
        // Setup
        final StartAgentParams startAgentParams = new StartAgentParams();
        startAgentParams.setScriptInParam("scriptInParam");
        startAgentParams.setScriptType("scriptType");
        startAgentParams.setScriptAgentInfoId(0L);
        startAgentParams.setScriptTaskIpsId(0L);
        startAgentParams.setScriptWorkDir("scriptWorkDir");
        startAgentParams.setScriptHashMap(new HashMap<>());
        startAgentParams.setBizId("bizId");
        startAgentParams.setDsId(0L);
        startAgentParams.setScriptTaskId(0L);
        startAgentParams.setScriptEnvironment("scriptEnvironment");
        startAgentParams.setSrcScriptUuid("srcScriptUuid");
        startAgentParams.setInfoUniqueUuid("infoUniqueUuid");
        startAgentParams.setAgentIp("agentIp");
        startAgentParams.setAgentPort(0);
        startAgentParams.setUserId("userId");
        startAgentParams.setAgentParam("agentParam");
        startAgentParams.setTaskRuntimeId(0L);
        startAgentParams.setExpectLastLine("expectLastline");
        startAgentParams.setErrorExpectLastLine("errorExpectLastLine");

        startAgentParams.setExpectType(0);
        startAgentParams.setExecUserName("execUser");

        // Run the test
        taskExecuteServiceImplUnderTest.organizeScriptHashtable(startAgentParams);

        // Verify the results
        assertEquals("expectLastline", startAgentParams.getScriptHashMap().get("expectLastline"));
        assertEquals(null, startAgentParams.getScriptHashMap().get("errorExpectLastLine"));
    }

    @Test
    void testMakeExpectLastLine_lastLine_expected() {
        // Setup
        final StartAgentParams startAgentParams = new StartAgentParams();
        startAgentParams.setScriptInParam("scriptInParam");
        startAgentParams.setScriptType("scriptType");
        startAgentParams.setScriptAgentInfoId(0L);
        startAgentParams.setScriptTaskIpsId(0L);
        startAgentParams.setScriptWorkDir("scriptWorkDir");
        startAgentParams.setScriptHashMap(new HashMap<>());
        startAgentParams.setBizId("bizId");
        startAgentParams.setDsId(0L);
        startAgentParams.setScriptTaskId(0L);
        startAgentParams.setScriptEnvironment("scriptEnvironment");
        startAgentParams.setSrcScriptUuid("srcScriptUuid");
        startAgentParams.setInfoUniqueUuid("infoUniqueUuid");
        startAgentParams.setAgentIp("agentIp");
        startAgentParams.setAgentPort(0);
        startAgentParams.setUserId("userId");
        startAgentParams.setAgentParam("agentParam");
        startAgentParams.setTaskRuntimeId(0L);
        startAgentParams.setExpectLastLine("expectLastline");
        startAgentParams.setErrorExpectLastLine("errorExpectLastLine");

        startAgentParams.setExpectType(1);
        startAgentParams.setExecUserName("execUser");

        // Run the test
        taskExecuteServiceImplUnderTest.makeExpectLastLine(startAgentParams);

        // Verify the results
        // Verify the results
        assertEquals("expectLastline", startAgentParams.getScriptHashMap().get("expectLastline"));
        assertEquals("", startAgentParams.getScriptHashMap().get("errorExpectLastline"));
    }

    @Test
    void testMakeExpectLastLine_lastLine_unExpected() {
        // Setup
        final StartAgentParams startAgentParams = new StartAgentParams();
        startAgentParams.setScriptInParam("scriptInParam");
        startAgentParams.setScriptType("scriptType");
        startAgentParams.setScriptAgentInfoId(0L);
        startAgentParams.setScriptTaskIpsId(0L);
        startAgentParams.setScriptWorkDir("scriptWorkDir");
        startAgentParams.setScriptHashMap(new HashMap<>());
        startAgentParams.setBizId("bizId");
        startAgentParams.setDsId(0L);
        startAgentParams.setScriptTaskId(0L);
        startAgentParams.setScriptEnvironment("scriptEnvironment");
        startAgentParams.setSrcScriptUuid("srcScriptUuid");
        startAgentParams.setInfoUniqueUuid("infoUniqueUuid");
        startAgentParams.setAgentIp("agentIp");
        startAgentParams.setAgentPort(0);
        startAgentParams.setUserId("userId");
        startAgentParams.setAgentParam("agentParam");
        startAgentParams.setTaskRuntimeId(0L);
        startAgentParams.setExpectLastLine("expectLastline");
        startAgentParams.setErrorExpectLastLine("errorExpectLastLine");

        startAgentParams.setExpectType(1);
        startAgentParams.setExecUserName("execUser");

        // Run the test
        taskExecuteServiceImplUnderTest.makeExpectLastLine(startAgentParams);

        // Verify the results
        // Verify the results
        assertEquals("expectLastline", startAgentParams.getScriptHashMap().get("expectLastline"));
        assertEquals("", startAgentParams.getScriptHashMap().get("errorExpectLastline"));
    }

    @Test
    void testMakeExpectLastLine_exitCode() {
        // Setup
        final StartAgentParams startAgentParams = new StartAgentParams();
        startAgentParams.setScriptInParam("scriptInParam");
        startAgentParams.setScriptType("scriptType");
        startAgentParams.setScriptAgentInfoId(0L);
        startAgentParams.setScriptTaskIpsId(0L);
        startAgentParams.setScriptWorkDir("scriptWorkDir");
        startAgentParams.setScriptHashMap(new HashMap<>());
        startAgentParams.setBizId("bizId");
        startAgentParams.setDsId(0L);
        startAgentParams.setScriptTaskId(0L);
        startAgentParams.setScriptEnvironment("scriptEnvironment");
        startAgentParams.setSrcScriptUuid("srcScriptUuid");
        startAgentParams.setInfoUniqueUuid("infoUniqueUuid");
        startAgentParams.setAgentIp("agentIp");
        startAgentParams.setAgentPort(0);
        startAgentParams.setUserId("userId");
        startAgentParams.setAgentParam("agentParam");
        startAgentParams.setTaskRuntimeId(0L);
        startAgentParams.setExpectLastLine("expectLastline");
        startAgentParams.setErrorExpectLastLine("errorExpectLastLine");

        startAgentParams.setExpectType(2);
        startAgentParams.setExecUserName("execUser");

        // Run the test
        taskExecuteServiceImplUnderTest.makeExpectLastLine(startAgentParams);

        // Verify the results
        // Verify the results
        assertEquals("expectLastline", startAgentParams.getScriptHashMap().get("expectLastline"));
        assertEquals("", startAgentParams.getScriptHashMap().get("errorExpectLastline"));
    }

    @Test
    void testGetSqlScriptContentWithParams() {
        assertThat(taskExecuteServiceImplUnderTest.getSqlScriptContentWithParams("scriptContent", "param"))
                .isEqualTo("scriptContent");
    }

    @Test
    void testGetBindAllTaskIpsInfo() {
        // Setup
        // Configure TaskIpsMapper.getBindAllTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean.setScriptTaskIpsId(0L);
        taskIpsAgentResultBean.setScriptAgentInfoId(0L);
        taskIpsAgentResultBean.setExecUserName("execUser");
        taskIpsAgentResultBean.setAgentIp("agentIp");
        taskIpsAgentResultBean.setAgentPort(0);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans = Arrays.asList(taskIpsAgentResultBean);
        when(mockTaskIpsMapper.getBindAllTaskIpsInfo(0L)).thenReturn(taskIpsAgentResultBeans);

        // Run the test
        final Long[] result = taskExecuteServiceImplUnderTest.getBindAllTaskIpsInfo(0L);

        // Verify the results
        assertThat(result).isEqualTo(new Long[] { 0L });
    }

    @Test
    void testGetBindAllTaskIpsInfo_TaskIpsMapperReturnsNoItems() {
        // Setup
        when(mockTaskIpsMapper.getBindAllTaskIpsInfo(0L)).thenReturn(Collections.emptyList());

        // Run the test
        final Long[] result = taskExecuteServiceImplUnderTest.getBindAllTaskIpsInfo(0L);

        // Verify the results
        assertThat(result).isEqualTo(new Long[] {});
    }

    @Test
    void testGetScriptTaskStartList() {
        // Setup
        final TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setRetryRuntimeId(0L);
        taskStartDto.setDriveMode(0);
        taskStartDto.setScriptTaskId(0L);
        taskStartDto.setSrcScriptUuid("srcScriptUuid");
        taskStartDto.setTaskName("scriptName");
        taskStartDto.setInfoUniqueUuid("infoUniqueUuid");
        taskStartDto.setScriptType("scriptType");
        taskStartDto.setIscriptTaskInstanceId(0L);
        taskStartDto.setTaskIps(new Long[] { 0L });
        taskStartDto.setRetry(false);

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setExpectType(0);

        infoVersionDto.setExpectLastline("expectLastline");
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(infoVersionDto);

        // Configure TaskIpsMapper.getTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean.setScriptTaskIpsId(0L);
        taskIpsAgentResultBean.setScriptAgentInfoId(0L);
        taskIpsAgentResultBean.setExecUserName("execUser");
        taskIpsAgentResultBean.setAgentIp("agentIp");
        taskIpsAgentResultBean.setAgentPort(0);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans = Arrays.asList(taskIpsAgentResultBean);
        when(mockTaskIpsMapper.getTaskIpsInfo(any(Long[].class), eq(0L))).thenReturn(taskIpsAgentResultBeans);

        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("scriptType");

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType("type");
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Arrays.asList(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(taskParams);
        TaskDto taskDto = new TaskDto();
        taskDto.setSrcScriptUuid("srcScriptUuid");
        taskDto.setTaskName("taskName");
        when(mockTaskService.selectTaskById(any(Long.class))).thenReturn(taskDto);
        // Run the test
        final List<StartAgentParams> result = taskExecuteServiceImplUnderTest.getScriptTaskStartList(taskStartDto);

        // Verify the results
        assertFalse(result.isEmpty()); // 断言返回的列表长度大于0
    }

    @Test
    void testGetScriptTaskStartList_TaskIpsMapperReturnsNull() {
        // Setup
        final TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setRetryRuntimeId(0L);
        taskStartDto.setDriveMode(0);
        taskStartDto.setScriptTaskId(0L);
        taskStartDto.setSrcScriptUuid("srcScriptUuid");
        taskStartDto.setTaskName("scriptName");
        taskStartDto.setInfoUniqueUuid("infoUniqueUuid");
        taskStartDto.setScriptType("scriptType");
        taskStartDto.setIscriptTaskInstanceId(0L);
        taskStartDto.setTaskIps(new Long[] { 0L });
        taskStartDto.setRetry(false);

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setExpectType(0);

        infoVersionDto.setExpectLastline("expectLastline");
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(infoVersionDto);

        when(mockTaskIpsMapper.getTaskIpsInfo(any(Long[].class), eq(0L))).thenReturn(null);
        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("scriptType");

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType("type");
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Arrays.asList(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(taskParams);
        TaskDto taskDto = new TaskDto();
        taskDto.setSrcScriptUuid("srcScriptUuid");
        taskDto.setTaskName("taskName");
        when(mockTaskService.selectTaskById(any(Long.class))).thenReturn(taskDto);
        // Run the test
        final List<StartAgentParams> result = taskExecuteServiceImplUnderTest.getScriptTaskStartList(taskStartDto);
        assertNotNull(result);
        // Verify the results
    }

    @Test
    void testGetScriptTaskStartList_TaskIpsMapperReturnsNoItems() {
        // Setup
        final TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setRetryRuntimeId(0L);
        taskStartDto.setDriveMode(0);
        taskStartDto.setScriptTaskId(0L);
        taskStartDto.setSrcScriptUuid("srcScriptUuid");
        taskStartDto.setTaskName("scriptName");
        taskStartDto.setInfoUniqueUuid("infoUniqueUuid");
        taskStartDto.setScriptType("scriptType");
        taskStartDto.setIscriptTaskInstanceId(0L);
        taskStartDto.setTaskIps(new Long[] { 0L });
        taskStartDto.setRetry(false);

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setExpectType(0);

        infoVersionDto.setExpectLastline("expectLastline");
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(infoVersionDto);

        when(mockTaskIpsMapper.getTaskIpsInfo(any(Long[].class), eq(0L))).thenReturn(Collections.emptyList());
        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("scriptType");

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType("type");
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Arrays.asList(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(taskParams);

        TaskDto taskDto = new TaskDto();
        taskDto.setSrcScriptUuid("srcScriptUuid");
        taskDto.setTaskName("taskName");
        when(mockTaskService.selectTaskById(any(Long.class))).thenReturn(taskDto);

        // Run the test
        final List<StartAgentParams> result = taskExecuteServiceImplUnderTest.getScriptTaskStartList(taskStartDto);

        // Verify the results
        assertThat(result).isEqualTo(Collections.emptyList());
    }

    @Test
    void testScriptShellKill() throws Exception {
        // Setup
        // Configure ITaskRuntimeService.getBindAgentForTaskRuntime(...).
        final TaskRunTimeBindAgentBean taskRunTimeBindAgentBean = new TaskRunTimeBindAgentBean();
        taskRunTimeBindAgentBean.setTaskRuntimeId(0L);
        taskRunTimeBindAgentBean.setSysmAgentInfoId(0L);
        taskRunTimeBindAgentBean.setTaskIpsId(0L);
        taskRunTimeBindAgentBean.setAgentIp("agentIp");
        taskRunTimeBindAgentBean.setAgentPort("15000");
        when(mockTaskRuntimeService.getBindAgentForTaskRuntime(0L)).thenReturn(taskRunTimeBindAgentBean);

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setTaskInstanceId(1L);
        taskRuntimeDto.setState(11);
        when(mockTaskRuntimeService.selectTaskRuntimeById(any(Long.class))).thenReturn(taskRuntimeDto);

        // Run the test
        taskExecuteServiceImplUnderTest.scriptShellKill(0L, new Long[] { 0L });

        // Verify the results
        verify(mockTaskRuntimeService).updateTaskRuntime(any(TaskRuntimeDto.class));

        // Confirm AgentOperateApi.send(...).
        final AgentOperateDto agentOperateDto = new AgentOperateDto();
        agentOperateDto.setContent("");
        agentOperateDto.setContentFormatter("contentFormatter");
        agentOperateDto.setBizId("bizId");
        agentOperateDto.setTargetId(0L);
        agentOperateDto.setTargetType(TargetTypeEnum.AGENT);
        agentOperateDto.setRpcMethod("IEAIAgent.executeAct");
        final DynamicResourcesDto dynamicResourcesDTO = new DynamicResourcesDto();
        agentOperateDto.setDynamicResourcesDTO(dynamicResourcesDTO);
        final AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost("agentIp");
        agentOptionDto.setAgentPort(0L);
        agentOperateDto.setAgentOptionDto(agentOptionDto);
        final List<AgentOperateDto> agentOperateList = Arrays.asList(agentOperateDto);
        // verify(mockAgentOperateApi).send(agentOperateList);
    }

    @Test
    void testScriptShellKill_ITaskRuntimeServiceGetBindAgentForTaskRuntimeReturnsNull() {
        // Setup
        when(mockTaskRuntimeService.getBindAgentForTaskRuntime(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(() -> taskExecuteServiceImplUnderTest.scriptShellKill(0L, new Long[] { 0L }))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testScriptShellKill_AgentOperateApiThrowsTaskSaveException() throws Exception {
        // Setup
        // Configure ITaskRuntimeService.getBindAgentForTaskRuntime(...).
        final TaskRunTimeBindAgentBean taskRunTimeBindAgentBean = new TaskRunTimeBindAgentBean();
        taskRunTimeBindAgentBean.setTaskRuntimeId(0L);
        taskRunTimeBindAgentBean.setSysmAgentInfoId(0L);
        taskRunTimeBindAgentBean.setTaskIpsId(0L);
        taskRunTimeBindAgentBean.setAgentIp("agentIp");
        taskRunTimeBindAgentBean.setAgentPort("15000");
        when(mockTaskRuntimeService.getBindAgentForTaskRuntime(0L)).thenReturn(taskRunTimeBindAgentBean);

        // Configure AgentOperateApi.send(...).
        final AgentOperateDto agentOperateDto = new AgentOperateDto();
        agentOperateDto.setContent("");
        agentOperateDto.setContentFormatter("contentFormatter");
        agentOperateDto.setBizId("bizId");
        agentOperateDto.setTargetId(0L);
        agentOperateDto.setTargetType(TargetTypeEnum.AGENT);
        agentOperateDto.setRpcMethod("IEAIAgent.executeAct");
        final DynamicResourcesDto dynamicResourcesDTO = new DynamicResourcesDto();
        agentOperateDto.setDynamicResourcesDTO(dynamicResourcesDTO);
        final AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost("agentIp");
        agentOptionDto.setAgentPort(0L);
        agentOperateDto.setAgentOptionDto(agentOptionDto);

        // 捕获 registerSynchronization 中传入的 TransactionSynchronization 对象
        TransactionSynchronization[] synchronization = new TransactionSynchronization[1];
        transactionSynchronizationManagerMockedStatic.when(
                () -> TransactionSynchronizationManager.registerSynchronization(any(TransactionSynchronization.class)))
                .thenAnswer(invocation -> {
                    synchronization[0] = invocation.getArgument(0);
                    return null;
                });

        when(mockAgentOperateApi.send(anyList())).thenThrow(TaskSaveException.class);

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setTaskInstanceId(1L);
        taskRuntimeDto.setState(11);
        when(mockTaskRuntimeService.selectTaskRuntimeById(any(Long.class))).thenReturn(taskRuntimeDto);
        // Run the test
        taskExecuteServiceImplUnderTest.scriptShellKill(0L, new Long[] { 0L });
        if (synchronization[0] != null) {
            synchronization[0].afterCompletion(0);
        }
        // Verify the results
        verify(mockTaskRuntimeService).updateTaskRuntime(any(TaskRuntimeDto.class));
    }

    @Test
    void testScriptTestExecution_IInfoServiceReturnsNull() {
        // Setup
        final ScriptTestExecutionDto testExecutionDto = new ScriptTestExecutionDto();
        final TaskParamsDto taskParamsDto = new TaskParamsDto();
        taskParamsDto.setId(0L);
        taskParamsDto.setScriptTaskId(0L);
        taskParamsDto.setStartType(0);
        testExecutionDto.setParams(Arrays.asList(taskParamsDto));
        testExecutionDto.setSrcScriptUuid("srcScriptUuid");
        testExecutionDto.setInfoId(0L);
        final AttachmentDto attachmentDto = new AttachmentDto();
        testExecutionDto.setScriptTempAttachments(Arrays.asList(attachmentDto));
        final AgentInfoDto agentInfoDto = new AgentInfoDto();
        testExecutionDto.setChosedAgentUsers(Arrays.asList(agentInfoDto));
        final TaskGroupsDto taskGroupsDto = new TaskGroupsDto();
        testExecutionDto.setChosedResGroups(Arrays.asList(taskGroupsDto));

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("loginName");

        when(mockInfoService.selectInfoById(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> taskExecuteServiceImplUnderTest.scriptTestExecution(testExecutionDto, user))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testScriptShellKillByRunTimeIds_ITaskRuntimeServiceGetBindAgentForTaskRuntimeReturnsNull() {
        // Setup
        when(mockTaskRuntimeService.getBindAgentForTaskRuntime(0L)).thenReturn(null);

        // Run the test
        assertThatThrownBy(
                () -> taskExecuteServiceImplUnderTest.scriptShellKillByRunTimeIds(0L, new Long[] { 0L }))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void testScriptShellKillByRunTimeIds_AgentOperateApiThrowsTaskSaveException() throws Exception {
        // Setup
        // Configure ITaskRuntimeService.getBindAgentForTaskRuntime(...).
        final TaskRunTimeBindAgentBean taskRunTimeBindAgentBean = new TaskRunTimeBindAgentBean();
        taskRunTimeBindAgentBean.setTaskRuntimeId(0L);
        taskRunTimeBindAgentBean.setSysmAgentInfoId(0L);
        taskRunTimeBindAgentBean.setTaskIpsId(0L);
        taskRunTimeBindAgentBean.setAgentIp("agentIp");
        taskRunTimeBindAgentBean.setAgentPort("15000");
        when(mockTaskRuntimeService.getBindAgentForTaskRuntime(0L)).thenReturn(taskRunTimeBindAgentBean);

        // Configure AgentOperateApi.send(...).
        final AgentOperateDto agentOperateDto = new AgentOperateDto();
        agentOperateDto.setContent("");
        agentOperateDto.setContentFormatter("contentFormatter");
        agentOperateDto.setBizId("bizId");
        agentOperateDto.setTargetId(0L);
        agentOperateDto.setTargetType(TargetTypeEnum.AGENT);
        agentOperateDto.setRpcMethod("IEAIAgent.executeAct");
        final DynamicResourcesDto dynamicResourcesDTO = new DynamicResourcesDto();
        agentOperateDto.setDynamicResourcesDTO(dynamicResourcesDTO);
        final AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost("agentIp");
        agentOptionDto.setAgentPort(0L);
        agentOperateDto.setAgentOptionDto(agentOptionDto);
        final List<AgentOperateDto> agentOperateList = Arrays.asList(agentOperateDto);
        when(mockAgentOperateApi.send(anyList())).thenThrow(TaskSaveException.class);
        // 捕获 registerSynchronization 中传入的 TransactionSynchronization 对象
        TransactionSynchronization[] synchronization = new TransactionSynchronization[1];
        transactionSynchronizationManagerMockedStatic.when(
                () -> TransactionSynchronizationManager.registerSynchronization(any(TransactionSynchronization.class)))
                .thenAnswer(invocation -> {
                    synchronization[0] = invocation.getArgument(0);
                    return null;
                });
        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setTaskInstanceId(1L);
        taskRuntimeDto.setState(11);
        when(mockTaskRuntimeService.selectTaskRuntimeById(any(Long.class))).thenReturn(taskRuntimeDto);
        // Run the test
        taskExecuteServiceImplUnderTest.scriptShellKillByRunTimeIds(0L, new Long[] { 0L });
        if (synchronization[0] != null) {
            synchronization[0].afterCompletion(0);
        }
        // Verify the results
        verify(mockTaskRuntimeService).updateTaskRuntime(any(TaskRuntimeDto.class));
    }

    @Test
    void testScriptTaskStartFormApply() throws Exception {
        // Setup
        final TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setRetryRuntimeId(0L);
        taskStartDto.setDriveMode(0);
        taskStartDto.setScriptTaskId(0L);
        taskStartDto.setSrcScriptUuid("srcScriptUuid");
        taskStartDto.setTaskName("scriptName");
        taskStartDto.setInfoUniqueUuid("infoUniqueUuid");
        taskStartDto.setScriptType("scriptType");
        taskStartDto.setIscriptTaskInstanceId(0L);
        taskStartDto.setTaskIps(new Long[] { 0L });
        taskStartDto.setRetry(false);
        taskStartDto.setScriptNameEn("script");
        taskStartDto.setEachNum(1);

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("loginName");

        // Configure ITaskService.selectTaskById(...).
        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(0);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(0L);
        taskDto.setSrcScriptUuid("srcScriptUuid");
        taskDto.setTaskName("scriptName");
        taskDto.setEachNum(0);
        taskDto.setTaskScheduler(0);
        taskDto.setTaskCron("taskCron");
        taskDto.setPublishDesc("publishDesc");
        taskDto.setTimeout(0L);
        taskDto.setType(0);
        taskDto.setDriveMode(0);
        when(mockTaskService.selectTaskById(0L)).thenReturn(taskDto);

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType("type");
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Arrays.asList(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(taskParams);

        when(mockTaskIpsMapper.getTotalAgentCountForTask(0L)).thenReturn(0);
        // when(mockTaskIpsMapper.getUnexecutedAgentCountForTask(0L)).thenReturn(0);
        when(mockTaskIpsMapper.getMaxOperIdForTask(0L)).thenReturn(0);

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setExpectType(0);

        infoVersionDto.setExpectLastline("expectLastline");
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(infoVersionDto);

        // Configure TaskIpsMapper.getTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean1 = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean1.setScriptTaskIpsId(0L);
        taskIpsAgentResultBean1.setScriptAgentInfoId(0L);
        taskIpsAgentResultBean1.setExecUserName("execUser");
        taskIpsAgentResultBean1.setAgentIp("agentIp");
        taskIpsAgentResultBean1.setAgentPort(0);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans1 = Arrays.asList(taskIpsAgentResultBean1);
        when(mockTaskIpsMapper.getTaskIpsInfo(any(Long[].class), eq(0L))).thenReturn(taskIpsAgentResultBeans1);

        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("scriptType");

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure InfoVersionTextMapper.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(0L);
        infoVersionText.setCreatorName("creatorName");
        when(mockInfoVersionTextMapper.selectInfoVersionTextByScriptUuid("srcScriptUuid")).thenReturn(infoVersionText);

        // Configure TaskAttachmentMapper.selectTaskAttachmentList(...).
        final TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(0L);
        taskAttachment.setScriptTaskId(0L);
        taskAttachment.setName("name");
        taskAttachment.setSize(0L);
        taskAttachment.setContents("content".getBytes());
        final List<TaskAttachment> taskAttachments = Arrays.asList(taskAttachment);
        when(mockTaskAttachmentMapper.selectTaskAttachmentList(any(TaskAttachment.class))).thenReturn(taskAttachments);

        when(mockRedissonClient.getAtomicLong(any(String.class))).thenReturn(mock(RAtomicLong.class));
        when(mockRedissonClient.getMap(anyString())).thenReturn(mock(RMap.class));

        // 当调用 getBucket 方法时，返回模拟的 RBucket<String> 对象
        when(mockRedissonClient.getBucket(any(String.class))).thenReturn(mock(RBucket.class));

        // 模拟批量插入方法
        doAnswer(invocation -> {
            // 获取传递给 batchData 方法的参数
            Class mapperClass = invocation.getArgument(0);
            List<TaskRuntime> taskRuntimeList = invocation.getArgument(1);
            Class entityClass = invocation.getArgument(2);
            String methodName = invocation.getArgument(3);
            SqlSession sqlSession = invocation.getArgument(4);
            TaskRuntimeMapper taskRuntimeMapper = invocation.getArgument(5);

            // 遍历 taskRuntimeList，并为每个 TaskRuntime 对象设置模拟的 ID 值
            for (TaskRuntime taskRuntime : taskRuntimeList) {
                long generatedId = 12345L; // 模拟生成的 ID 值
                taskRuntime.setId(generatedId);
            }

            // 返回模拟的结果，可以是 null 或者其他适当的返回值
            return null;
        }).when(mockBatchDataUtil).batchData(
                any(), // 模拟方法的第一个参数是 TaskRuntimeMapper 类
                any(), // 模拟方法的第二个参数是一个列表，类型为 TaskRuntime
                any(), // 模拟方法的第三个参数是 TaskRuntime 类
                any(), // 模拟方法的第四个参数是方法名字符串
                any(), // 模拟方法的第五个参数是 SqlSession 类
                any() // 模拟方法的第六个参数是 TaskRuntimeMapper 类
        );

        // 模拟插入操作后生成的ID值
        final long generatedId = 12345L;
        doAnswer((Answer<Integer>) invocation -> {
            TaskInstanceDto taskInstanceDto = invocation.getArgument(0);
            taskInstanceDto.setId(generatedId);
            return 1; // 模拟插入操作成功返回1
        }).when(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class));
        when(mockInfoService.selectInfoByUniqueUuid(any())).thenReturn(new ScriptInfoDto());

        // Run the test
        final Long result = taskExecuteServiceImplUnderTest.scriptTaskStartFormApply(taskStartDto, user);

        // Verify the results
        assertNotNull(result);

        // Confirm BatchDataUtil.batchData(...).
        final TaskRuntime taskRuntime = new TaskRuntime();
        taskRuntime.setBizId("bizId");
        taskRuntime.setScriptTaskIpsId(0L);
        taskRuntime.setAgentTaskId(0L);
        taskRuntime.setId(0L);
        taskRuntime.setScriptTaskId(0L);
        final List<TaskRuntime> listEntity = Arrays.asList(taskRuntime);

        // Confirm AgentOperateApi.send(...).
        final AgentOperateDto agentOperateDto = new AgentOperateDto();
        agentOperateDto.setContent("");
        agentOperateDto.setContentFormatter("contentFormatter");
        agentOperateDto.setBizId("bizId");
        agentOperateDto.setTargetId(0L);
        agentOperateDto.setTargetType(TargetTypeEnum.AGENT);
        agentOperateDto.setRpcMethod("IEAIAgent.executeAct");
        final DynamicResourcesDto dynamicResourcesDTO = new DynamicResourcesDto();
        agentOperateDto.setDynamicResourcesDTO(dynamicResourcesDTO);
        final AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost("agentIp");
        agentOptionDto.setAgentPort(0L);
        agentOperateDto.setAgentOptionDto(agentOptionDto);
        final List<AgentOperateDto> agentOperateList = Arrays.asList(agentOperateDto);
        // verify(mockAgentOperateApi).send(agentOperateList);
    }

    @Test
    void testSkipScriptShell() throws Exception {
        // Setup
        // Configure ITaskRuntimeService.selectTaskRuntimeById(...).
        final TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setScriptTaskIpsId(0L);
        taskRuntimeDto.setId(0L);
        taskRuntimeDto.setScriptTaskId(0L);
        taskRuntimeDto.setTaskInstanceId(0L);
        taskRuntimeDto.setSrcScriptUuid("srcScriptUuid");
        taskRuntimeDto.setState(0);
        taskRuntimeDto.setScriptName("scriptName");
        taskRuntimeDto.setAgentIp("agentIp");
        taskRuntimeDto.setExecUser("execUser");
        taskRuntimeDto.setBizId("bizId");
        taskRuntimeDto.setStartTime(Timestamp.valueOf(LocalDateTime.of(2020, 1, 1, 0, 0, 0, 0)));
        taskRuntimeDto.setExpectLastline("expectLastline");

        taskRuntimeDto.setTimeoutValue(0L);
        taskRuntimeDto.setAgentPort(0);
        taskRuntimeDto.setExpectType(0);
        taskRuntimeDto.setStartType(0);
        when(mockTaskRuntimeService.selectTaskRuntimeById(0L)).thenReturn(taskRuntimeDto);

        // Run the test
        taskExecuteServiceImplUnderTest.skipScriptShell(0L, new Long[] { 0L });

        // Verify the results
        verify(mockTaskRuntimeService).updateExecTimeAndState(eq("0"), eq(5), any(TaskRuntimeDto.class));
        verify(mockTaskInstanceService).updateTaskInstanceState(anyLong());
    }

    @Test
    void testStopTask() throws Exception {

        // Configure ITaskRuntimeService.getBindAgentForTaskRuntime(...).
        final TaskRunTimeBindAgentBean taskRunTimeBindAgentBean = new TaskRunTimeBindAgentBean();
        taskRunTimeBindAgentBean.setTaskRuntimeId(0L);
        taskRunTimeBindAgentBean.setSysmAgentInfoId(0L);
        taskRunTimeBindAgentBean.setTaskIpsId(0L);
        taskRunTimeBindAgentBean.setAgentIp("agentIp");
        taskRunTimeBindAgentBean.setAgentPort("15000");
        when(mockTaskRuntimeService.getBindAgentForTaskRuntime(0L)).thenReturn(taskRunTimeBindAgentBean);

        TaskInstanceDto taskInstanceDto = new TaskInstanceDto();
        taskInstanceDto.setId(1L);
        when(mockTaskInstanceService.selectTaskInstanceById(any(Long.class))).thenReturn(taskInstanceDto);

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setTaskInstanceId(1L);
        when(mockTaskRuntimeService.selectTaskRuntimeById(any(Long.class))).thenReturn(taskRuntimeDto);

        List<Long> resultArray = new ArrayList<>();
        resultArray.add(1L);

        when(mockTaskInstanceService.selectTaskInstanceIdsById(any())).thenReturn(resultArray);

        // Run the test
        taskExecuteServiceImplUnderTest.stopTask(new Long[] { 0L });

    }

    @Test
    void testScriptShellKillByRunTimeIds() throws Exception {
        // Setup
        // Configure ITaskRuntimeService.getBindAgentForTaskRuntime(...).
        final TaskRunTimeBindAgentBean taskRunTimeBindAgentBean = new TaskRunTimeBindAgentBean();
        taskRunTimeBindAgentBean.setTaskRuntimeId(0L);
        taskRunTimeBindAgentBean.setSysmAgentInfoId(0L);
        taskRunTimeBindAgentBean.setTaskIpsId(0L);
        taskRunTimeBindAgentBean.setAgentIp("agentIp");
        taskRunTimeBindAgentBean.setAgentPort("15000");
        when(mockTaskRuntimeService.getBindAgentForTaskRuntime(0L)).thenReturn(taskRunTimeBindAgentBean);

        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setTaskInstanceId(1L);
        taskRuntimeDto.setState(11);
        when(mockTaskRuntimeService.selectTaskRuntimeById(any(Long.class))).thenReturn(taskRuntimeDto);
        // Run the test
        taskExecuteServiceImplUnderTest.scriptShellKillByRunTimeIds(0L, new Long[] { 0L });

        // Verify the results
        verify(mockTaskRuntimeService).updateTaskRuntime(any(TaskRuntimeDto.class));

        // Confirm AgentOperateApi.send(...).
        final AgentOperateDto agentOperateDto = new AgentOperateDto();
        agentOperateDto.setContent("");
        agentOperateDto.setContentFormatter("contentFormatter");
        agentOperateDto.setBizId("bizId");
        agentOperateDto.setTargetId(0L);
        agentOperateDto.setTargetType(TargetTypeEnum.AGENT);
        agentOperateDto.setRpcMethod("IEAIAgent.executeAct");
        final DynamicResourcesDto dynamicResourcesDTO = new DynamicResourcesDto();
        agentOperateDto.setDynamicResourcesDTO(dynamicResourcesDTO);
        final AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost("agentIp");
        agentOptionDto.setAgentPort(0L);
        agentOperateDto.setAgentOptionDto(agentOptionDto);
        final List<AgentOperateDto> agentOperateList = Arrays.asList(agentOperateDto);
        // verify(mockAgentOperateApi).send(agentOperateList);
    }

    @Test
    void testScriptWhiteTaskStart() throws Exception {
        // Setup
        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("loginName");

        final TaskDto taskInfo = new TaskDto();
        taskInfo.setScriptTaskSource(0);
        taskInfo.setStartType(0);
        taskInfo.setReadyToExecute(0);
        taskInfo.setId(0L);
        taskInfo.setSrcScriptUuid("srcScriptUuid");
        taskInfo.setTaskName("scriptName");
        taskInfo.setEachNum(0);
        taskInfo.setTaskScheduler(0);
        taskInfo.setTaskCron("taskCron");
        taskInfo.setPublishDesc("publishDesc");
        taskInfo.setTimeout(0L);
        taskInfo.setType(0);
        taskInfo.setDriveMode(0);

        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("scriptType");

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setExpectType(0);
        infoVersionDto.setExpectLastline("expectLastline");
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(infoVersionDto);

        // Configure ITaskService.selectTaskById(...).
        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(0);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(0L);
        taskDto.setSrcScriptUuid("srcScriptUuid");
        taskDto.setTaskName("scriptName");
        taskDto.setEachNum(0);
        taskDto.setTaskScheduler(0);
        taskDto.setTaskCron("taskCron");
        taskDto.setPublishDesc("publishDesc");
        taskDto.setTimeout(0L);
        taskDto.setType(0);
        taskDto.setDriveMode(0);
        when(mockTaskService.selectTaskById(0L)).thenReturn(taskDto);

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType("type");
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Arrays.asList(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(taskParams);

        when(mockTaskIpsMapper.getTotalAgentCountForTask(0L)).thenReturn(0);

        when(mockTaskIpsMapper.getMaxOperIdForTask(0L)).thenReturn(0);

        // Configure TaskIpsMapper.getTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean1 = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean1.setScriptTaskIpsId(0L);
        taskIpsAgentResultBean1.setScriptAgentInfoId(0L);
        taskIpsAgentResultBean1.setExecUserName("execUser");
        taskIpsAgentResultBean1.setAgentIp("agentIp");
        taskIpsAgentResultBean1.setAgentPort(0);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans1 = Arrays.asList(taskIpsAgentResultBean1);
        when(mockTaskIpsMapper.getTaskIpsInfo(any(Long[].class), eq(0L))).thenReturn(taskIpsAgentResultBeans1);

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure InfoVersionTextMapper.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(0L);
        infoVersionText.setCreatorName("creatorName");
        when(mockInfoVersionTextMapper.selectInfoVersionTextByScriptUuid("srcScriptUuid")).thenReturn(infoVersionText);

        // Configure TaskAttachmentMapper.selectTaskAttachmentList(...).
        final TaskAttachment taskAttachment = new TaskAttachment();
        taskAttachment.setId(0L);
        taskAttachment.setScriptTaskId(0L);
        taskAttachment.setName("name");
        taskAttachment.setSize(0L);
        taskAttachment.setContents("content".getBytes());
        final List<TaskAttachment> taskAttachments = Arrays.asList(taskAttachment);
        when(mockTaskAttachmentMapper.selectTaskAttachmentList(any(TaskAttachment.class))).thenReturn(taskAttachments);

        when(mockRedissonClient.getAtomicLong(any(String.class))).thenReturn(mock(RAtomicLong.class));
        when(mockRedissonClient.getMap(anyString())).thenReturn(mock(RMap.class));
        // 当调用 getBucket 方法时，返回模拟的 RBucket<String> 对象
        when(mockRedissonClient.getBucket(any(String.class))).thenReturn(mock(RBucket.class));
        // 模拟批量插入方法
        doAnswer(invocation -> {
            // 获取传递给 batchData 方法的参数
            Class mapperClass = invocation.getArgument(0);
            List<TaskRuntime> taskRuntimeList = invocation.getArgument(1);
            Class entityClass = invocation.getArgument(2);
            String methodName = invocation.getArgument(3);
            SqlSession sqlSession = invocation.getArgument(4);
            TaskRuntimeMapper taskRuntimeMapper = invocation.getArgument(5);

            // 遍历 taskRuntimeList，并为每个 TaskRuntime 对象设置模拟的 ID 值
            for (TaskRuntime taskRuntime : taskRuntimeList) {
                long generatedId = 12345L; // 模拟生成的 ID 值
                taskRuntime.setId(generatedId);
            }

            // 返回模拟的结果，可以是 null 或者其他适当的返回值
            return null;
        }).when(mockBatchDataUtil).batchData(
                any(), // 模拟方法的第一个参数是 TaskRuntimeMapper 类
                any(), // 模拟方法的第二个参数是一个列表，类型为 TaskRuntime
                any(), // 模拟方法的第三个参数是 TaskRuntime 类
                any(), // 模拟方法的第四个参数是方法名字符串
                any(), // 模拟方法的第五个参数是 SqlSession 类
                any() // 模拟方法的第六个参数是 TaskRuntimeMapper 类
        );

        // 模拟插入操作后生成的ID值
        final long generatedId = 12345L;
        doAnswer((Answer<Integer>) invocation -> {
            TaskInstanceDto taskInstanceDto = invocation.getArgument(0);
            taskInstanceDto.setId(generatedId);
            return 1; // 模拟插入操作成功返回1
        }).when(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class));

        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptName("scriptName");
        when(mockInfoService.selectInfoByUniqueUuid(any())).thenReturn(scriptInfoDto);
        // Run the test
        final Long result = taskExecuteServiceImplUnderTest.scriptWhiteTaskStart(user, taskInfo, "srcScriptUuid");

        // Verify the results
        assertNotNull(result);
        // verify(mockTaskIpsMapper).updateIpsStatusAndIncrementOperId(any(Long[].class),
        // eq(1));

        // Confirm BatchDataUtil.batchData(...).
        final TaskRuntime taskRuntime = new TaskRuntime();
        taskRuntime.setBizId("bizId");
        taskRuntime.setScriptTaskIpsId(0L);
        taskRuntime.setAgentTaskId(0L);
        taskRuntime.setId(0L);
        taskRuntime.setScriptTaskId(0L);
        final List<TaskRuntime> listEntity = Arrays.asList(taskRuntime);

        // Confirm AgentOperateApi.send(...).
        final AgentOperateDto agentOperateDto = new AgentOperateDto();
        agentOperateDto.setContent("");
        agentOperateDto.setContentFormatter("contentFormatter");
        agentOperateDto.setBizId("bizId");
        agentOperateDto.setTargetId(0L);
        agentOperateDto.setTargetType(TargetTypeEnum.AGENT);
        agentOperateDto.setRpcMethod("IEAIAgent.executeAct");
        final DynamicResourcesDto dynamicResourcesDTO = new DynamicResourcesDto();
        agentOperateDto.setDynamicResourcesDTO(dynamicResourcesDTO);
        final AgentOptionDto agentOptionDto = new AgentOptionDto();
        agentOptionDto.setAgentHost("agentIp");
        agentOptionDto.setAgentPort(0L);
        agentOperateDto.setAgentOptionDto(agentOptionDto);
        final List<AgentOperateDto> agentOperateList = Arrays.asList(agentOperateDto);

    }

    @Test
    void cancelTask() throws ScriptException {
        Long scriptTaskId = 1L;
        Long scheduleId = 10L;

        TaskDto taskDto = new TaskDto();
        taskDto.setTaskName("taskName");
        taskDto.setTaskCron("*/5 * * * *");
        taskDto.setTaskScheduler(1);

        TaskScheduleDto taskScheduleDto = new TaskScheduleDto();
        taskScheduleDto.setScheduleId(1L);
        when(taskScheduleService.selectTaskScheduleByTaskId(any(Long.class))).thenReturn(taskScheduleDto);
        when(mockTaskService.selectTaskById(any(Long.class))).thenReturn(taskDto);
        when(jobOperateService.stopJob(any(Integer.class))).thenReturn(true);

        // Act
        taskExecuteServiceImplUnderTest.cancelTask(scriptTaskId);
        verify(mockTaskService).selectTaskById(any(Long.class));

    }

    @Test
    @DisplayName("测试获取脚本实时输出")
    void getRealTimeOutPutMessage() throws SendException {
        // 模拟所需的输入
        Long taskRuntimeId = 1L;
        TaskRuntimeDto taskRuntimeDto = new TaskRuntimeDto();
        taskRuntimeDto.setId(1L);
        taskRuntimeDto.setBizId("testBizId");
        taskRuntimeDto.setAgentIp("testAgentIp");
        taskRuntimeDto.setAgentPort(1234);

        // 创建一个ShellCmdOutput对象并设置属性
        ShellCmdOutput shellCmdOutput = new ShellCmdOutput();
        shellCmdOutput.setStdOut("c3RkT3V0"); // Base64编码的"stdOut"
        shellCmdOutput.setStdErr("c3RkRXJy"); // Base64编码的"stdErr"

        // 使用XStream将对象转换为XML
        XStream xstream = new XStream();
        xstream.allowTypes(new Class[] { ShellCmdOutput.class });
        xstream.alias("ShellCmdOutput", ShellCmdOutput.class);
        xstream.aliasField("_stdOut", ShellCmdOutput.class, "stdOut");
        xstream.aliasField("_stdErr", ShellCmdOutput.class, "stdErr");
        String xml = xstream.toXML(shellCmdOutput);

        AgentSyncOperateResultDto agentSyncOperateResultDto = new AgentSyncOperateResultDto();
        agentSyncOperateResultDto.setContent(xml);

        // 配置mock对象行为
        when(mockTaskRuntimeService.selectTaskRuntimeById(taskRuntimeId)).thenReturn(taskRuntimeDto);
        when(mockAgentOperateApi.sendSync(any(AgentOperateDto.class))).thenReturn(agentSyncOperateResultDto);

        // Mock Base64.getFromBase64 方法，确保它返回正确的值
        try (MockedStatic<Base64> mockedBase64 = mockStatic(Base64.class)) {
            mockedBase64.when(() -> Base64.getFromBase64("c3RkT3V0")).thenReturn("stdOut");
            mockedBase64.when(() -> Base64.getFromBase64("c3RkRXJy")).thenReturn("stdErr");

            // 调用被测试的方法
            String result = taskExecuteServiceImplUnderTest.getRealTimeOutPutMessage(taskRuntimeId);

            // 验证预期结果
            assertThat(result).isEqualTo("stdOutstdErr");
        }

        // 验证方法调用
        verify(mockTaskRuntimeService).selectTaskRuntimeById(taskRuntimeId);
        verify(mockAgentOperateApi).sendSync(any(AgentOperateDto.class));
    }

    @Test
    void timeTaskSwitch() throws Exception {
        // 异常测试
        // 1.查询查询 xxl-job的id异常
        long id = 1L;
        int state = 1;

        TaskScheduleDto taskScheduleDto = new TaskScheduleDto();
        taskScheduleDto.setScheduleId(1l);

        // 1.前台传递state无效异常 ScriptExpection
        when(taskScheduleService.selectTaskScheduleByTaskId(id)).thenReturn(taskScheduleDto);
        try {
            taskExecuteServiceImplUnderTest.timeTaskSwitch(id, state);
        } catch (ScriptException e) {
            System.out.println("定时、周期切换state异常通过");
        } catch (Exception e) {
            throw new Exception("测试未通过");
        }

        // 2.调用xxljob启动 失败异常校验
        when(taskScheduleService.selectTaskScheduleByTaskId(id)).thenReturn(taskScheduleDto);
        when(jobOperateService.startJob(1)).thenReturn(false);
        try {
            taskExecuteServiceImplUnderTest.timeTaskSwitch(id, state);
        } catch (ScriptException e) {
            System.out.println("定时、周期切换xxl-job失败抛异常通过");
        } catch (Exception e) {
            throw new Exception("测试未通过");
        }

        // 3.调用xxljob启动 成功
        when(taskScheduleService.selectTaskScheduleByTaskId(id)).thenReturn(taskScheduleDto);
        when(jobOperateService.startJob(1)).thenReturn(true);
        try {
            taskExecuteServiceImplUnderTest.timeTaskSwitch(id, state);
        } catch (Exception e) {
            throw new Exception("测试未通过");
        }

        // 4.调用xxljob停止 失败异常校验
        when(taskScheduleService.selectTaskScheduleByTaskId(id)).thenReturn(taskScheduleDto);
        when(jobOperateService.startJob(1)).thenReturn(false);
        try {
            taskExecuteServiceImplUnderTest.timeTaskSwitch(id, state);
        } catch (ScriptException e) {
            System.out.println("定时、周期切换xxl-job失败抛异常通过");
        } catch (Exception e) {
            throw new Exception("测试未通过");
        }

        // 5.调用xxljob停止 成功
        when(taskScheduleService.selectTaskScheduleByTaskId(id)).thenReturn(taskScheduleDto);
        when(jobOperateService.startJob(1)).thenReturn(true);
        try {
            taskExecuteServiceImplUnderTest.timeTaskSwitch(id, state);
        } catch (Exception e) {
            throw new Exception("测试未通过");
        }

        // 断言
        verify(jobOperateService, times(5)).startJob(1);
    }

    @Test
    void timeTaskKill() throws Exception {
        //
        long id = 1L;
        List<Long> instanceIds = new ArrayList<Long>();
        instanceIds.add(1L);
        TaskScheduleDto taskScheduleDto = new TaskScheduleDto();
        taskScheduleDto.setScheduleId(1L);

        // 1.终止过程中，同时有人发起终止单次任务 导致实例无法查询
        when(taskScheduleService.selectTaskScheduleByTaskId(1L)).thenReturn(taskScheduleDto);
        when(taskMapper.updateTask(any())).thenReturn(1);
        when(taskInstanceMapper.selectRunningTaskByScriptTaskId(1L)).thenReturn(instanceIds);

        doThrow(new ScriptException()).when(iTaskExecuteService).stopTask(any());
        try {
            taskExecuteServiceImplUnderTest.timeTaskKill(id);
        } catch (ScriptException e) {
            System.out.println("stopTask抛出异常");
        }

        // 2.成功运行，无运行中任务
        when(taskScheduleService.selectTaskScheduleByTaskId(1L)).thenReturn(taskScheduleDto);
        when(taskMapper.updateTask(any())).thenReturn(1);
        when(taskInstanceMapper.selectRunningTaskByScriptTaskId(1L)).thenReturn(new ArrayList<>());
        taskExecuteServiceImplUnderTest.timeTaskKill(id);

        // 3.成功运行，调用stopTask方法不进行测试 stopTask有自己的单元测试方法

        // 断言
        verify(taskMapper, times(2)).updateTask(any());
    }

    @Test
    void updateTimeTaskCron() throws Exception {
        // 1.周期类型任务, xxljob 更新成功
        TaskExecuteDto taskExecuteDto = new TaskExecuteDto();
        taskExecuteDto.setScriptTaskId(1L);
        taskExecuteDto.setTaskCron("0 0 0 * * ? *");
        taskExecuteDto.setTaskScheduler(1);

        TaskDto taskDto = new TaskDto();
        taskDto.setId(1L);
        taskDto.setTaskName("123");
        taskDto.setTaskCron("0 0 0 * * ? *");
        taskDto.setCreatorName("123");
        taskDto.setCreatorId(1L);

        TaskScheduleDto taskScheduleDto = new TaskScheduleDto();
        taskScheduleDto.setScheduleId(1L);
        when(mockTaskService.selectTaskById(1L)).thenReturn(taskDto);
        when(taskScheduleService.selectTaskScheduleByTaskId(1L)).thenReturn(taskScheduleDto);
        when(jobOperateService.modifyJob(any())).thenReturn(true);
        taskExecuteServiceImplUnderTest.updateTimeTaskCron(taskExecuteDto);

        // 2.周期类型任务, xxljob 更新失败
        when(mockTaskService.selectTaskById(1L)).thenReturn(taskDto);
        when(taskScheduleService.selectTaskScheduleByTaskId(1L)).thenReturn(taskScheduleDto);
        when(jobOperateService.modifyJob(any())).thenReturn(false);
        assertThatThrownBy(() -> taskExecuteServiceImplUnderTest.updateTimeTaskCron(taskExecuteDto))
                .isInstanceOf(ScriptException.class);

        // 3.定时类型任务， xxljob更新成功
        TaskExecuteDto taskExecuteDto1 = new TaskExecuteDto();
        taskExecuteDto1.setScriptTaskId(1L);
        taskExecuteDto1.setTaskTime(new Timestamp(1719532800000l));
        taskExecuteDto1.setTaskScheduler(1);
        when(mockTaskService.selectTaskById(1L)).thenReturn(taskDto);
        when(taskScheduleService.selectTaskScheduleByTaskId(1L)).thenReturn(taskScheduleDto);
        when(jobOperateService.modifyJob(any())).thenReturn(true);
        taskExecuteServiceImplUnderTest.updateTimeTaskCron(taskExecuteDto1);

        // 4.定时类型任务，xxljob更新失败
        when(mockTaskService.selectTaskById(1L)).thenReturn(taskDto);
        when(taskScheduleService.selectTaskScheduleByTaskId(1L)).thenReturn(taskScheduleDto);
        when(jobOperateService.modifyJob(any())).thenReturn(false);
        assertThatThrownBy(() -> taskExecuteServiceImplUnderTest.updateTimeTaskCron(taskExecuteDto))
                .isInstanceOf(ScriptException.class);
    }

    @Test
    void selectTimeTasks() {
        // Prepare test data
        TaskExecuteQueryDto taskExecuteQueryDto = new TaskExecuteQueryDto();
        taskExecuteQueryDto.setCategoryId(1L);

        CurrentUser currentUser = new CurrentUser();
        currentUser.setId(1L);
        currentUser.setLoginName("admin");

        List<Long> idList = new ArrayList<>();
        idList.add(1L);
        idList.add(2L);

        final TaskExecuteBean taskExecuteBean = new TaskExecuteBean();
        taskExecuteBean.setScriptTaskInstanceId(0L);
        taskExecuteBean.setRunAgentCount(0);
        taskExecuteBean.setScriptTaskInstanceState(0);
        taskExecuteBean.setScriptCategoryName("scriptCategoryName");
        taskExecuteBean.setCategoryId(0L);
        Page<TaskExecuteBean> page = new Page<>();
        page.add(taskExecuteBean);

        when(mockCategoryService.getAllCategoryIds(1L)).thenReturn(idList);
        when(mockTaskService.selectTimeTaskList(any(TaskExecuteBean.class), any(CurrentUser.class))).thenReturn(page);

        PageInfo<TaskExecuteDto> taskExecuteDtoPageInfo = taskExecuteServiceImplUnderTest
                .selectTimeTasks(taskExecuteQueryDto, 1, 15, currentUser);
        assertNotNull(taskExecuteDtoPageInfo);

    }

    @Test
    void stopScriptTaskByTaskInstanceId() {
        List<Long> taskInstanceIdList = new ArrayList<>();
        taskInstanceIdList.add(1L);
        StopScriptTasksApiDto stopScriptTasksApiDto = taskExecuteServiceImplUnderTest
                .stopScriptTaskByTaskInstanceId(taskInstanceIdList);

        // 断言
        assertNotNull(stopScriptTasksApiDto);
    }

    @Test
    @DisplayName("测试导出Agent历史记录Excel - 成功场景")
    void testExportAgentHisExcel_Success() throws Exception {
        // Setup
        List<Long> taskInstanceIds = Arrays.asList(1L, 2L);
        MockHttpServletResponse response = new MockHttpServletResponse();

        // 准备模拟数据
        List<TaskHisAgentExcelDto> mockExportData = new ArrayList<>();
        TaskHisAgentExcelDto dto1 = new TaskHisAgentExcelDto();
        dto1.setTaskName("Task1");
        dto1.setScriptNameZh("脚本1");
        dto1.setScriptName("script1");
        dto1.setAgentIp("***********");
        dto1.setAgentPort(8080);
        dto1.setAgentName("agent1");
        dto1.setDeviceName("device1");
        dto1.setOsName("Linux");
        dto1.setCenterName("中心1");
        dto1.setAgentState("1");
        dto1.setStartTime(new Timestamp(System.currentTimeMillis()));
        dto1.setEndTime(new Timestamp(System.currentTimeMillis() + 1000));
        dto1.setIstdout("执行成功");

        TaskHisAgentExcelDto dto2 = new TaskHisAgentExcelDto();
        dto2.setTaskName("Task2");
        dto2.setScriptNameZh("脚本2");
        dto2.setScriptName("script2");
        dto2.setAgentIp("***********");
        dto2.setAgentPort(8081);
        dto2.setAgentName("agent2");
        dto2.setDeviceName("device2");
        dto2.setOsName("Windows");
        dto2.setCenterName("中心2");
        dto2.setAgentState("2");
        dto2.setStartTime(new Timestamp(System.currentTimeMillis()));
        dto2.setEndTime(new Timestamp(System.currentTimeMillis() + 2000));
        dto2.setIstdout("执行失败");

        mockExportData.add(dto1);
        mockExportData.add(dto2);

        // Mock taskMapper
        when(taskMapper.getTaskHisExcelExport(taskInstanceIds))
                .thenReturn(mockExportData);

        // Execute
        taskExecuteServiceImplUnderTest.exportAgentHisExcel(taskInstanceIds, response);

        // Verify
        // 验证响应头
        assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8",
                response.getContentType());
        String fileName = URLEncoder.encode("任务监控执行历史", "UTF-8");
        assertEquals("attachment;filename=" + fileName + ".xlsx",
                response.getHeader("Content-disposition"));

        // 验证Excel内容
        byte[] content = response.getContentAsByteArray();
        try (InputStream is = new ByteArrayInputStream(content)) {
            List<Map<Integer, String>> dataList = EasyExcel.read(is).sheet("agent执行历史").doReadSync();

            // 验证数据行数（包含表头）
            assertEquals(2, dataList.size());

            // 验证数据内容
            Map<Integer, String> firstRow = dataList.get(1);
            assertEquals("Task2", firstRow.get(2)); // taskName
            assertEquals("***********", firstRow.get(5)); // agentIp
            assertEquals("8081", firstRow.get(6)); // agentPort
            assertEquals("agent2", firstRow.get(7)); // agentName
        }

        // 验证方法调用
        verify(taskMapper).getTaskHisExcelExport(taskInstanceIds);
    }

    @Test
    @DisplayName("测试导出Agent历史记录Excel - 空数据场景")
    void testExportAgentHisExcel_EmptyData() throws Exception {
        // Setup
        List<Long> taskInstanceIds = Arrays.asList(1L, 2L);
        MockHttpServletResponse response = new MockHttpServletResponse();

        // Mock空数据返回
        when(taskMapper.getTaskHisExcelExport(taskInstanceIds))
                .thenReturn(Collections.emptyList());

        // Execute
        taskExecuteServiceImplUnderTest.exportAgentHisExcel(taskInstanceIds, response);

        // Verify
        // 验证响应头
        assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8",
                response.getContentType());
        String fileName = URLEncoder.encode("任务监控执行历史", "UTF-8");
        assertEquals("attachment;filename=" + fileName + ".xlsx",
                response.getHeader("Content-disposition"));

        // 验证Excel内容
        byte[] content = response.getContentAsByteArray();
        try (InputStream is = new ByteArrayInputStream(content)) {
            List<Map<Integer, String>> dataList = EasyExcel.read(is).sheet("agent执行历史").doReadSync();

            // 只有表头，没有数据
            assertEquals(0, dataList.size());
        }

        // 验证方法调用
        verify(taskMapper).getTaskHisExcelExport(taskInstanceIds);
    }

    @Test
    @DisplayName("测试导出Agent历史记录Excel - 异常场景")
    void testExportAgentHisExcel_Exception() throws Exception {
        // Setup
        List<Long> taskInstanceIds = Arrays.asList(1L, 2L);
        MockHttpServletResponse response = new MockHttpServletResponse();

        // Mock异常
        when(taskMapper.getTaskHisExcelExport(taskInstanceIds))
                .thenThrow(new RuntimeException("Database error"));

        // Execute & Verify
        assertThrows(RuntimeException.class,
                () -> taskExecuteServiceImplUnderTest.exportAgentHisExcel(taskInstanceIds, response));

        // 验证方法调用
        verify(taskMapper).getTaskHisExcelExport(taskInstanceIds);
    }

    @Test
    @DisplayName("测试导出Agent历史记录Excel - 参数为null场景")
    void testExportAgentHisExcel_NullParams() throws Exception {
        // Setup
        MockHttpServletResponse response = new MockHttpServletResponse();

        // Mock null返回
        when(taskMapper.getTaskHisExcelExport(null))
                .thenReturn(Collections.emptyList());

        // Execute
        taskExecuteServiceImplUnderTest.exportAgentHisExcel(null, response);

        // Verify
        // 验证响应头
        assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8",
                response.getContentType());
        String fileName = URLEncoder.encode("任务监控执行历史", "UTF-8");
        assertEquals("attachment;filename=" + fileName + ".xlsx",
                response.getHeader("Content-disposition"));

        // 验证Excel内容
        byte[] content = response.getContentAsByteArray();
        try (InputStream is = new ByteArrayInputStream(content)) {
            List<Map<Integer, String>> dataList = EasyExcel.read(is).sheet("agent执行历史").doReadSync();

            // 只有表头，没有数据
            assertEquals(0, dataList.size());
        }

        // 验证方法调用
        verify(taskMapper).getTaskHisExcelExport(null);
    }

    @Test
    @DisplayName("测试buildAgent方法 - 正常场景")
    void testBuildAgent() throws Exception {
        // 获取私有方法
        Method buildAgentMethod = TaskExecuteServiceImpl.class.getDeclaredMethod("buildAgent", List.class);
        buildAgentMethod.setAccessible(true);

        // 准备测试数据
        List<TaskExecuteBean> taskExecuteBeanList = new ArrayList<>();
        TaskExecuteBean bean1 = new TaskExecuteBean();
        bean1.setScriptTaskId(1L);
        TaskExecuteBean bean2 = new TaskExecuteBean();
        bean2.setScriptTaskId(2L);
        taskExecuteBeanList.add(bean1);
        taskExecuteBeanList.add(bean2);

        // Mock agentInfoService的返回值
        List<AgentInfoDto> agentList1 = new ArrayList<>();
        AgentInfoDto agent1 = new AgentInfoDto();
        agent1.setAgentIp("***********");
        agent1.setAgentPort(8080);
        AgentInfoDto agent2 = new AgentInfoDto();
        agent2.setAgentIp("***********");
        agent2.setAgentPort(8081);
        agentList1.add(agent1);
        agentList1.add(agent2);

        List<AgentInfoDto> agentList2 = new ArrayList<>();
        AgentInfoDto agent3 = new AgentInfoDto();
        agent3.setAgentIp("***********");
        agent3.setAgentPort(8082);
        agentList2.add(agent3);

        when(agentInfoService.selectAgentInfoByServiceId(null, 1L)).thenReturn(agentList1);
        when(agentInfoService.selectAgentInfoByServiceId(null, 2L)).thenReturn(agentList2);

        // 执行私有方法
        buildAgentMethod.invoke(taskExecuteServiceImplUnderTest, taskExecuteBeanList);

        // 验证结果
        assertEquals("***********:8080,***********:8081", bean1.getAgent());
        assertEquals("***********:8082", bean2.getAgent());

        // 验证方法调用
        verify(agentInfoService).selectAgentInfoByServiceId(null, 1L);
        verify(agentInfoService).selectAgentInfoByServiceId(null, 2L);
    }

    @Test
    @DisplayName("测试buildAgent方法 - 空列表场景")
    void testBuildAgent_EmptyList() throws Exception {
        // 获取私有方法
        Method buildAgentMethod = TaskExecuteServiceImpl.class.getDeclaredMethod("buildAgent", List.class);
        buildAgentMethod.setAccessible(true);

        // 准备空列表
        List<TaskExecuteBean> emptyList = new ArrayList<>();

        // 执行私有方法
        buildAgentMethod.invoke(taskExecuteServiceImplUnderTest, emptyList);

        // 验证没有调用agentInfoService
        verify(agentInfoService, never()).selectAgentInfoByServiceId(any(), any());
    }

    @Test
    @DisplayName("测试buildAgent方法 - Agent列表为空场景")
    void testBuildAgent_EmptyAgentList() throws Exception {
        // 获取私有方法
        Method buildAgentMethod = TaskExecuteServiceImpl.class.getDeclaredMethod("buildAgent", List.class);
        buildAgentMethod.setAccessible(true);

        // 准备测试数据
        List<TaskExecuteBean> taskExecuteBeanList = new ArrayList<>();
        TaskExecuteBean bean = new TaskExecuteBean();
        bean.setScriptTaskId(1L);
        taskExecuteBeanList.add(bean);

        // Mock返回空的agent列表
        when(agentInfoService.selectAgentInfoByServiceId(null, 1L))
                .thenReturn(Collections.emptyList());

        // 执行私有方法
        buildAgentMethod.invoke(taskExecuteServiceImplUnderTest, taskExecuteBeanList);

        // 验证结果
        assertEquals("", bean.getAgent());

        // 验证方法调用
        verify(agentInfoService).selectAgentInfoByServiceId(null, 1L);
    }

    @Test
    @DisplayName("测试buildAgent方法 - ScriptTaskId为null场景")
    void testBuildAgent_NullScriptTaskId() throws Exception {
        // 获取私有方法
        Method buildAgentMethod = TaskExecuteServiceImpl.class.getDeclaredMethod("buildAgent", List.class);
        buildAgentMethod.setAccessible(true);

        // 准备测试数据
        List<TaskExecuteBean> taskExecuteBeanList = new ArrayList<>();
        TaskExecuteBean bean = new TaskExecuteBean();
        bean.setScriptTaskId(null);
        taskExecuteBeanList.add(bean);

        // Mock返回空的agent列表
        when(agentInfoService.selectAgentInfoByServiceId(null, null))
                .thenReturn(Collections.emptyList());

        // 执行私有方法
        buildAgentMethod.invoke(taskExecuteServiceImplUnderTest, taskExecuteBeanList);

        // 验证结果
        assertEquals("", bean.getAgent());

        // 验证方法调用
        verify(agentInfoService).selectAgentInfoByServiceId(null, null);
    }

    @Test
    @DisplayName("测试buildAgent方法 - Agent信息部分为null场景")
    void testBuildAgent_NullAgentInfo() throws Exception {
        // 获取私有方法
        Method buildAgentMethod = TaskExecuteServiceImpl.class.getDeclaredMethod("buildAgent", List.class);
        buildAgentMethod.setAccessible(true);

        // 准备测试数据
        List<TaskExecuteBean> taskExecuteBeanList = new ArrayList<>();
        TaskExecuteBean bean = new TaskExecuteBean();
        bean.setScriptTaskId(1L);
        taskExecuteBeanList.add(bean);

        // 准备包含null值的agent列表
        List<AgentInfoDto> agentList = new ArrayList<>();
        AgentInfoDto agent1 = new AgentInfoDto();
        agent1.setAgentIp(null);
        agent1.setAgentPort(8080);
        AgentInfoDto agent2 = new AgentInfoDto();
        agent2.setAgentIp("***********");
        agent2.setAgentPort(null);
        agentList.add(agent1);
        agentList.add(agent2);

        when(agentInfoService.selectAgentInfoByServiceId(null, 1L))
                .thenReturn(agentList);

        // 执行私有方法
        buildAgentMethod.invoke(taskExecuteServiceImplUnderTest, taskExecuteBeanList);

        // 验证结果
        assertEquals("null:8080,***********:null", bean.getAgent());

        // 验证方法调用
        verify(agentInfoService).selectAgentInfoByServiceId(null, 1L);
    }

    @Test
    @DisplayName("测试导出Excel - 正常场景")
    void testExportExcel_Success() {
        // 准备测试数据
        List<Long> ids = Arrays.asList(1L, 2L);
        MockHttpServletResponse response = new MockHttpServletResponse();

        // Mock CurrentUser
        CurrentUser mockUser = new CurrentUser();
        mockUser.setId(1L);
        mockUser.setFullName("testUser");
        try (MockedStatic<CurrentUserUtil> mockedStatic = mockStatic(CurrentUserUtil.class)) {
            mockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(mockUser);

            // Mock taskMapper返回数据
            List<TaskExecuteBean> mockTaskList = new ArrayList<>();
            TaskExecuteBean task1 = new TaskExecuteBean();
            task1.setScriptTaskId(1L);
            task1.setTaskName("Task1");
            task1.setReadyToExecute(1);

            TaskExecuteBean task2 = new TaskExecuteBean();
            task2.setScriptTaskId(2L);
            task2.setTaskName("Task2");
            task2.setReadyToExecute(0);

            mockTaskList.add(task1);
            mockTaskList.add(task2);

            when(taskMapper.selectTaskByIds(ids, mockUser)).thenReturn(mockTaskList);

            // Mock agent信息
            List<AgentInfoDto> agentList1 = new ArrayList<>();
            AgentInfoDto agent1 = new AgentInfoDto();
            agent1.setAgentIp("***********");
            agent1.setAgentPort(8080);
            when(agentInfoService.selectAgentInfoByServiceId(null, 1L)).thenReturn(agentList1);

            List<AgentInfoDto> agentList2 = new ArrayList<>();
            AgentInfoDto agent2 = new AgentInfoDto();
            agent2.setAgentIp("***********");
            agent2.setAgentPort(8081);
            when(agentInfoService.selectAgentInfoByServiceId(null, 2L)).thenReturn(agentList2);

            // 执行测试
            taskExecuteServiceImplUnderTest.exportExcel(ids, response);

            // 验证响应头
            assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
                    response.getContentType());
            assertFalse(response.getHeader("Content-disposition").contains("定时任务维护"));

            // 验证方法调用
            verify(taskMapper).selectTaskByIds(ids, mockUser);
            verify(agentInfoService).selectAgentInfoByServiceId(null, 1L);
            verify(agentInfoService).selectAgentInfoByServiceId(null, 2L);
        }
    }

    @Test
    @DisplayName("测试导出Excel - 空数据场景")
    void testExportExcel_EmptyData() {
        // 准备测试数据
        List<Long> ids = Collections.emptyList();
        MockHttpServletResponse response = new MockHttpServletResponse();

        // Mock CurrentUser
        CurrentUser mockUser = new CurrentUser();
        try (MockedStatic<CurrentUserUtil> mockedStatic = mockStatic(CurrentUserUtil.class)) {
            mockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(mockUser);

            // Mock空数据返回
            when(taskMapper.selectTaskByIds(ids, mockUser)).thenReturn(Collections.emptyList());

            // 执行测试
            taskExecuteServiceImplUnderTest.exportExcel(ids, response);

            // 验证响应头
            assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
                    response.getContentType());
            assertFalse(response.getHeader("Content-disposition").contains("定时任务维护"));

            // 验证方法调用
            verify(taskMapper).selectTaskByIds(ids, mockUser);
            verify(agentInfoService, never()).selectAgentInfoByServiceId(any(), any());
        }
    }

    @Test
    @DisplayName("测试导出Excel - null参数场景")
    void testExportExcel_NullParams() {
        // 准备测试数据
        MockHttpServletResponse response = new MockHttpServletResponse();

        // Mock CurrentUser
        CurrentUser mockUser = new CurrentUser();
        try (MockedStatic<CurrentUserUtil> mockedStatic = mockStatic(CurrentUserUtil.class)) {
            mockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(mockUser);

            // Mock返回空列表
            when(taskMapper.selectTaskByIds(null, mockUser)).thenReturn(Collections.emptyList());

            // 执行测试
            taskExecuteServiceImplUnderTest.exportExcel(null, response);

            // 验证响应头
            assertEquals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8",
                    response.getContentType());
            assertFalse(response.getHeader("Content-disposition").contains("定时任务维护"));

            // 验证方法调用
            verify(taskMapper).selectTaskByIds(null, mockUser);
            verify(agentInfoService, never()).selectAgentInfoByServiceId(any(), any());
        }
    }

    @Test
    @DisplayName("测试导出Excel - ReadyToExecute为null场景")
    void testExportExcel_NullReadyToExecute() {
        // 准备测试数据
        List<Long> ids = Collections.singletonList(1L);
        MockHttpServletResponse response = new MockHttpServletResponse();

        // Mock CurrentUser
        CurrentUser mockUser = new CurrentUser();
        try (MockedStatic<CurrentUserUtil> mockedStatic = mockStatic(CurrentUserUtil.class)) {
            mockedStatic.when(CurrentUserUtil::getCurrentUser).thenReturn(mockUser);

            // Mock返回数据，ReadyToExecute为null
            List<TaskExecuteBean> mockTaskList = new ArrayList<>();
            TaskExecuteBean task = new TaskExecuteBean();
            task.setScriptTaskId(1L);
            task.setTaskName("Task1");
            task.setReadyToExecute(null);
            mockTaskList.add(task);

            when(taskMapper.selectTaskByIds(ids, mockUser)).thenReturn(mockTaskList);

            // Mock agent信息
            when(agentInfoService.selectAgentInfoByServiceId(null, 1L))
                    .thenReturn(Collections.emptyList());

            // 执行测试
            taskExecuteServiceImplUnderTest.exportExcel(ids, response);

            // 验证ReadyToExecute被设置为0
            ArgumentCaptor<List<TimeTaskExportBean>> captor = ArgumentCaptor.forClass(List.class);
            // 这里需要根据实际情况验证ExcelUtil.writeExcel的调用，如果ExcelUtil是静态方法可能需要使用PowerMockito

            // 验证方法调用
            verify(taskMapper).selectTaskByIds(ids, mockUser);
            verify(agentInfoService).selectAgentInfoByServiceId(null, 1L);
        }
    }

    @Test
    @DisplayName("测试加密Agent参数 - 启用加密")
    void testEncryptAgentParamIfNeeded_WithEncryption() throws Exception {
        // 准备测试数据
        StartAgentParams startAgentParams = new StartAgentParams();
        startAgentParams.setAgentParam("testParam");
        startAgentParams.setScriptHashMap(new HashMap<>());

        // 模拟配置返回true，表示需要加密
        when(scriptBusinessConfig.isSendScriptParametersDesEnc()).thenReturn(true);

        // 使用PowerMockito模拟DesUtils
        try (MockedConstruction<DesUtils> mocked = Mockito.mockConstruction(DesUtils.class,
                (mock, context) -> {
                    when(mock.encrypt(anyString())).thenReturn("encrypted_value");
                })) {

            // 使用反射调用私有方法
            Method method = TaskExecuteServiceImpl.class.getDeclaredMethod("encryptAgentParamIfNeeded",
                    StartAgentParams.class);
            method.setAccessible(true);
            method.invoke(taskExecuteServiceImplUnderTest, startAgentParams);

            // 验证结果
            assertNotNull(startAgentParams.getScriptHashMap());
            assertTrue(startAgentParams.getScriptHashMap()
                    .containsKey(AgentExecuteKey.AgentScriptHashtableKey.PM.getValue()));
            assertEquals("encrypted_value",
                    startAgentParams.getScriptHashMap().get(AgentExecuteKey.AgentScriptHashtableKey.PM.getValue()));
        }
    }

    @Test
    @DisplayName("测试加密Agent参数 - 禁用加密")
    void testEncryptAgentParamIfNeeded_WithoutEncryption() throws Exception {
        // 准备测试数据
        StartAgentParams startAgentParams = new StartAgentParams();
        startAgentParams.setAgentParam("testParam");
        startAgentParams.setScriptHashMap(new HashMap<>());

        // 模拟配置返回false，表示不需要加密
        when(scriptBusinessConfig.isSendScriptParametersDesEnc()).thenReturn(false);

        // 使用反射调用私有方法
        Method method = TaskExecuteServiceImpl.class.getDeclaredMethod("encryptAgentParamIfNeeded",
                StartAgentParams.class);
        method.setAccessible(true);
        method.invoke(taskExecuteServiceImplUnderTest, startAgentParams);

        // 验证结果
        assertFalse(startAgentParams.getScriptHashMap().isEmpty());
    }

    @Test
    @DisplayName("测试加密Agent参数 - 空参数")
    void testEncryptAgentParamIfNeeded_NullParam() throws Exception {
        // 准备测试数据
        StartAgentParams startAgentParams = new StartAgentParams();
        startAgentParams.setAgentParam(null);
        startAgentParams.setScriptHashMap(new HashMap<>());

        // 模拟配置返回true，表示需要加密
        when(scriptBusinessConfig.isSendScriptParametersDesEnc()).thenReturn(true);

        // 使用PowerMockito模拟DesUtils
        try (MockedConstruction<DesUtils> mocked = Mockito.mockConstruction(DesUtils.class,
                (mock, context) -> {
                    when(mock.encrypt(anyString())).thenReturn("encrypted_empty");
                })) {

            // 使用反射调用私有方法
            Method method = TaskExecuteServiceImpl.class.getDeclaredMethod("encryptAgentParamIfNeeded",
                    StartAgentParams.class);
            method.setAccessible(true);
            method.invoke(taskExecuteServiceImplUnderTest, startAgentParams);

            // 验证结果
            assertNotNull(startAgentParams.getScriptHashMap());
            assertTrue(startAgentParams.getScriptHashMap()
                    .containsKey(AgentExecuteKey.AgentScriptHashtableKey.PM.getValue()));
            assertNotEquals(null,
                    startAgentParams.getScriptHashMap().get(AgentExecuteKey.AgentScriptHashtableKey.PM.getValue()));
        }
    }

    @Test
    @DisplayName("测试加密Agent参数 - 加密异常")
    void testEncryptAgentParamIfNeeded_EncryptionException() throws Exception {
        // 准备测试数据
        StartAgentParams startAgentParams = new StartAgentParams();
        startAgentParams.setAgentParam("testParam");
        startAgentParams.setScriptHashMap(new HashMap<>());

        // 模拟配置返回true，表示需要加密
        when(scriptBusinessConfig.isSendScriptParametersDesEnc()).thenReturn(true);

        // 使用PowerMockito模拟DesUtils并抛出异常
        try (MockedConstruction<DesUtils> mocked = Mockito.mockConstruction(DesUtils.class,
                (mock, context) -> {
                    when(mock.encrypt(anyString())).thenThrow(new IllegalBlockSizeException("Test exception"));
                })) {

            // 使用反射调用私有方法
            Method method = TaskExecuteServiceImpl.class.getDeclaredMethod("encryptAgentParamIfNeeded",
                    StartAgentParams.class);
            method.setAccessible(true);
            method.invoke(taskExecuteServiceImplUnderTest, startAgentParams);

            // 验证结果 - 即使发生异常也不应该影响程序继续执行
            assertNotNull(startAgentParams.getScriptHashMap());
            // 验证异常情况下的处理结果
            assertFalse(startAgentParams.getScriptHashMap()
                    .containsKey(AgentExecuteKey.AgentScriptHashtableKey.PM.getValue()));
        }
    }

    private TaskIpsAgentResultBean createTaskIpsAgentResultBean(Long scriptTaskIpsId) {
        TaskIpsAgentResultBean bean = new TaskIpsAgentResultBean();
        bean.setScriptTaskIpsId(scriptTaskIpsId);
        bean.setScriptAgentInfoId(scriptTaskIpsId);
        bean.setExecUserName("execUser");
        bean.setAgentIp("agentIp");
        bean.setAgentPort(0);
        return bean;
    }

    @Test
    @DisplayName("测试队列模式保存Redis IPs - 正常情况")
    void testQueueModeSaveRedisIps_Success() throws Exception {
        // 准备测试数据
        TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setScriptTaskId(123L);
        taskStartDto.setEachNum(2);

        // 模拟首批IPs和非首批IPs
        Long[] mockFirstBatchIps = { 1L, 2L };
        List<Long> mockNotFirstBatchIps = Arrays.asList(3L, 4L, 5L);

        // 创建RQueue mock
        @SuppressWarnings("unchecked")
        RQueue<Long> mockQueue = mock(RQueue.class);

        // 模拟getExecTaskIpsInfo方法需要的数据
        List<TaskIpsAgentResultBean> mockFirstBatchBeans = Arrays.asList(
                createTaskIpsAgentResultBean(1L),
                createTaskIpsAgentResultBean(2L));
        when(mockTaskIpsMapper.getBindExecTaskIpsInfo(eq(123L))).thenReturn(mockFirstBatchBeans);

        // 模拟TaskIpsMapper.getNotFirstBatchIpsId方法
        List<TaskIpsAgentResultBean> mockNotFirstBatchBeans = Arrays.asList(
                createTaskIpsAgentResultBean(3L),
                createTaskIpsAgentResultBean(4L),
                createTaskIpsAgentResultBean(5L));
        when(mockTaskIpsMapper.getNotFirstBatchIpsId(eq(123L), eq(mockFirstBatchIps)))
                .thenReturn(mockNotFirstBatchBeans);

        // 模拟Redis相关操作
        doReturn(mockQueue).when(mockRedissonClient).getQueue(String.format(Constants.SCRIPT_TASK_EXEC_REDIS_QUEUE_KEY, 123L));
        doReturn(true).when(mockQueue).addAll(mockNotFirstBatchIps);
        doReturn(true).when(mockQueue).expire(any(Duration.class));

        // 设置taskStartDto的instanceId用于生成Redis key
        taskStartDto.setIscriptTaskInstanceId(123L);

        // 使用反射调用私有方法
        Method queueModeSaveRedisIpsMethod = TaskExecuteServiceImpl.class.getDeclaredMethod("queueModeSaveRedisIps",
                TaskStartDto.class);
        queueModeSaveRedisIpsMethod.setAccessible(true);
        Long[] result = (Long[]) queueModeSaveRedisIpsMethod.invoke(taskExecuteServiceImplUnderTest, taskStartDto);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.length);
        assertArrayEquals(mockFirstBatchIps, result);

        // 验证方法调用
        verify(mockRedissonClient).getQueue(String.format(Constants.SCRIPT_TASK_EXEC_REDIS_QUEUE_KEY, 123L));
        verify(mockQueue).addAll(mockNotFirstBatchIps);
        verify(mockQueue).expire(Duration.ofDays(Constants.EXEC_TASK_TIMEOUT_DAY));
    }

    @Test
    void timeTaskSwitch_StateTwo_StopJobFail() throws Exception {
        // 准备测试数据
        long id = 1L;
        int state = 2;

        TaskScheduleDto taskScheduleDto = new TaskScheduleDto();
        taskScheduleDto.setScheduleId(1L);

        // 模拟服务调用
        when(taskScheduleService.selectTaskScheduleByTaskId(id)).thenReturn(taskScheduleDto);
        when(jobOperateService.stopJob(1)).thenReturn(false);

        // 验证异常抛出
        assertThrows(ScriptException.class, () -> {
            taskExecuteServiceImplUnderTest.timeTaskSwitch(id, state);
        });

        // 验证方法调用
        verify(taskScheduleService).selectTaskScheduleByTaskId(id);
        verify(jobOperateService).stopJob(1);
    }

    @Test
    void timeTaskSwitch_StateTwo_StopJobSuccess() throws Exception {
        // 准备测试数据
        long id = 1L;
        int state = 2;

        TaskScheduleDto taskScheduleDto = new TaskScheduleDto();
        taskScheduleDto.setScheduleId(1L);

        // 模拟服务调用
        when(taskScheduleService.selectTaskScheduleByTaskId(id)).thenReturn(taskScheduleDto);
        when(jobOperateService.stopJob(1)).thenReturn(true);
        when(taskMapper.updateTask(any(Task.class))).thenReturn(1);

        // 执行测试
        taskExecuteServiceImplUnderTest.timeTaskSwitch(id, state);

        // 验证方法调用
        verify(taskScheduleService).selectTaskScheduleByTaskId(id);
        verify(jobOperateService).stopJob(1);

        // 验证Task对象的状态更新
        ArgumentCaptor<Task> taskCaptor = ArgumentCaptor.forClass(Task.class);
        verify(taskMapper).updateTask(taskCaptor.capture());
        Task capturedTask = taskCaptor.getValue();
        assertEquals(id, capturedTask.getId());
        assertEquals(Enums.ReadyToExecute.NOT_READY_TO_EXECUTE.getCode(), capturedTask.getReadyToExecute());

        // 验证TaskSchedule对象的状态更新
        ArgumentCaptor<TaskScheduleDto> scheduleCaptor = ArgumentCaptor.forClass(TaskScheduleDto.class);
        verify(taskScheduleService).updateTaskSchedule(scheduleCaptor.capture());
        TaskScheduleDto capturedSchedule = scheduleCaptor.getValue();
        assertEquals(taskScheduleDto.getScheduleId(), capturedSchedule.getId());
        assertEquals(Enums.TaskScheduleEnum.TASK_INIT.getValue(), capturedSchedule.getState());
    }

    @Test
    void testGetNotFirstBatchIpsId() {

        List<TaskIpsAgentResultBean> list = new ArrayList<>();
        TaskIpsAgentResultBean taskIpsAgentResultBean = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean.setScriptTaskIpsId(1L);
        list.add(taskIpsAgentResultBean);

        when(mockTaskIpsMapper.getNotFirstBatchIpsId(any(), any())).thenReturn(list);
        // Run the test
        final List<Long> result = taskExecuteServiceImplUnderTest.getNotFirstBatchIpsId(any(), any());

        // Verify the results
        assertFalse(result.isEmpty());
    }

    @ParameterizedTest
    @MethodSource("provideScriptTaskStartParams")
    @DisplayName("测试scriptTaskStart方法-参数化测试")
    void testScriptTaskStart_parameterized(String testCase, Long instanceId, boolean isRetry,
                                          boolean hasAttachments, int taskSource,
                                          boolean expectException, String expectedExceptionMsg) throws Exception {
        // Setup
        final TaskStartDto taskStartDto = new TaskStartDto();
        taskStartDto.setRetryRuntimeId(isRetry ? 1L : 0L);
        taskStartDto.setDriveMode(0);
        taskStartDto.setScriptTaskId(0L);
        taskStartDto.setSrcScriptUuid("srcScriptUuid");
        taskStartDto.setTaskName("scriptName");
        taskStartDto.setInfoUniqueUuid("infoUniqueUuid");
        taskStartDto.setScriptType("scriptType");
        taskStartDto.setIscriptTaskInstanceId(0L);
        taskStartDto.setTaskIps(new Long[] { 0L });
        taskStartDto.setRetry(isRetry);
        taskStartDto.setEachNum(1);
        if (isRetry) {
            taskStartDto.setFirstBatch(true);
        }

        final CurrentUser user = new CurrentUser();
        user.setId(0L);
        user.setLoginName("loginName");

        // Configure ITaskService.selectTaskById(...).
        final TaskDto taskDto = new TaskDto();
        taskDto.setScriptTaskSource(taskSource);
        taskDto.setStartType(0);
        taskDto.setReadyToExecute(0);
        taskDto.setId(0L);
        taskDto.setSrcScriptUuid("srcScriptUuid");
        taskDto.setTaskName("scriptName");
        taskDto.setEachNum(0);
        taskDto.setTaskScheduler(0);
        taskDto.setTaskCron("taskCron");
        taskDto.setPublishDesc("publishDesc");
        taskDto.setTimeout(0L);
        taskDto.setType(0);
        taskDto.setDriveMode(0);
        when(mockTaskService.selectTaskById(0L)).thenReturn(taskDto);

        // Configure TaskParamsMapper.selectTaskParamsList(...).
        final TaskParams taskParams1 = new TaskParams();
        taskParams1.setId(0L);
        taskParams1.setScriptTaskId(0L);
        taskParams1.setScriptParameterCheckId(0L);
        taskParams1.setType("type");
        taskParams1.setValue("value");
        final List<TaskParams> taskParams = Collections.singletonList(taskParams1);
        when(mockTaskParamsMapper.selectTaskParamsList(any(TaskParams.class))).thenReturn(taskParams);

        when(mockTaskIpsMapper.getTotalAgentCountForTask(0L)).thenReturn(0);
        when(mockTaskIpsMapper.getMaxOperIdForTask(0L)).thenReturn(0);

        // Configure IInfoVersionService.selectInfoVersionBySrcScriptUuid(...).
        final ScriptVersionDto infoVersionDto = new ScriptVersionDto();
        infoVersionDto.setId(0L);
        infoVersionDto.setInfoUniqueUuid("infoUniqueUuid");
        infoVersionDto.setExpectType(0);
        infoVersionDto.setExpectLastline("expectLastline");
        when(mockInfoVersionService.selectInfoVersionBySrcScriptUuid("srcScriptUuid")).thenReturn(infoVersionDto);

        // Configure TaskIpsMapper.getTaskIpsInfo(...).
        final TaskIpsAgentResultBean taskIpsAgentResultBean1 = new TaskIpsAgentResultBean();
        taskIpsAgentResultBean1.setScriptTaskIpsId(0L);
        taskIpsAgentResultBean1.setScriptAgentInfoId(0L);
        taskIpsAgentResultBean1.setExecUserName("execUser");
        taskIpsAgentResultBean1.setAgentIp("agentIp");
        taskIpsAgentResultBean1.setAgentPort(0);
        final List<TaskIpsAgentResultBean> taskIpsAgentResultBeans1 = Collections
                .singletonList(taskIpsAgentResultBean1);
        when(mockTaskIpsMapper.getTaskIpsInfo(any(Long[].class), eq(0L))).thenReturn(taskIpsAgentResultBeans1);

        // Configure TaskIpsMapper.getBindExecTaskIpsInfo(...).
        final TaskIpsAgentResultBean bindExecTaskIpsBean = new TaskIpsAgentResultBean();
        bindExecTaskIpsBean.setScriptTaskIpsId(0L);
        bindExecTaskIpsBean.setScriptAgentInfoId(0L);
        bindExecTaskIpsBean.setExecUserName("execUser");
        bindExecTaskIpsBean.setAgentIp("agentIp");
        bindExecTaskIpsBean.setAgentPort(0);
        final List<TaskIpsAgentResultBean> bindExecTaskIpsBeans = Arrays.asList(bindExecTaskIpsBean);
        when(mockTaskIpsMapper.getBindExecTaskIpsInfo(any(Long.class))).thenReturn(bindExecTaskIpsBeans);

        when(mockInfoVersionService.getScriptTypeBySrcScriptUuid("srcScriptUuid")).thenReturn("scriptType");

        // Configure SqlSessionFactory.openSession(...).
        final SqlSession mockSqlSession = mock(SqlSession.class);
        when(mockFactory.openSession(ExecutorType.BATCH, false)).thenReturn(mockSqlSession);

        // Configure InfoVersionTextMapper.selectInfoVersionTextByScriptUuid(...).
        final InfoVersionText infoVersionText = new InfoVersionText();
        infoVersionText.setId(0L);
        infoVersionText.setSrcScriptUuid("srcScriptUuid");
        infoVersionText.setContent("content");
        infoVersionText.setCreatorId(0L);
        infoVersionText.setCreatorName("creatorName");
        when(mockInfoVersionTextMapper.selectInfoVersionTextByScriptUuid("srcScriptUuid")).thenReturn(infoVersionText);

        // Configure TaskAttachmentMapper.selectTaskAttachmentList(...).
        final List<TaskAttachment> taskAttachments;
        if (hasAttachments) {
            final TaskAttachment taskAttachment = new TaskAttachment();
            taskAttachment.setId(0L);
            taskAttachment.setScriptTaskId(0L);
            taskAttachment.setName("name");
            taskAttachment.setSize(0L);
            taskAttachment.setContents("content".getBytes());
            taskAttachments = Collections.singletonList(taskAttachment);
        } else {
            taskAttachments = new ArrayList<>();
        }
        when(mockTaskAttachmentMapper.selectTaskAttachmentList(any(TaskAttachment.class))).thenReturn(taskAttachments);

        when(mockRedissonClient.getAtomicLong(any(String.class))).thenReturn(mock(RAtomicLong.class));
        when(mockRedissonClient.getMap(anyString())).thenReturn(mock(RMap.class));
        when(mockRedissonClient.getBucket(any(String.class))).thenReturn(mock(RBucket.class));

        // 模拟插入操作后生成的ID值
        doAnswer((Answer<Integer>) invocation -> {
            TaskInstanceDto taskInstanceDto = invocation.getArgument(0);
            taskInstanceDto.setId(instanceId);
            return 1; // 模拟插入操作成功返回1
        }).when(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class));

        // 创建spy对象来mock自引用方法
        TaskExecuteServiceImpl spyService = spy(taskExecuteServiceImplUnderTest);
        ReflectionTestUtils.setField(spyService, "iTaskExecuteService", spyService);

        // 模拟updateBatchStartAndCheck方法返回值
        StartAgentCommonParam mockStartAgentCommonParam = new StartAgentCommonParam();
        mockStartAgentCommonParam.setEachNum(2);
        mockStartAgentCommonParam.setTotal(1);
        mockStartAgentCommonParam.setTaskIpIds("0");
        mockStartAgentCommonParam.setParams("");
        mockStartAgentCommonParam.setScriptInParam("");
        doReturn(mockStartAgentCommonParam).when(spyService).updateBatchStartAndCheck(any(TaskStartDto.class));

        // 模拟批量插入方法
        doAnswer(invocation -> {
            List<TaskRuntime> taskRuntimeList = invocation.getArgument(1);
            for (TaskRuntime taskRuntime : taskRuntimeList) {
                long generatedId = 12345L;
                taskRuntime.setId(generatedId);
            }
            return null;
        }).when(mockBatchDataUtil).batchData(any(), any(), any(), any(), any(), any());

        when(scriptBusinessConfig.isSendScriptParametersDesEnc()).thenReturn(false);
        ScriptInfoDto scriptInfoDto = new ScriptInfoDto();
        scriptInfoDto.setScriptName("scriptName");
        when(mockInfoService.selectInfoByUniqueUuid(any())).thenReturn(scriptInfoDto);

        // Run the test
        if (expectException) {
            ScriptException exception = assertThrows(ScriptException.class, () -> {
                spyService.scriptTaskStart(taskStartDto, user);
            });
            assertEquals(expectedExceptionMsg, exception.getMessage());
        } else {
            final Long result = spyService.scriptTaskStart(taskStartDto, user);
            assertNotNull(result);
            // 验证调用次数根据实际业务逻辑调整
            verify(mockTaskService, atLeastOnce()).updateTask(any(TaskDto.class));
            verify(mockTaskInstanceService).insertTaskInstance(any(TaskInstanceDto.class));
        }
    }

    /**
     * 为参数化测试提供参数
     */
    static Stream<Arguments> provideScriptTaskStartParams() {
        return Stream.of(
            Arguments.of("正常启动-有附件", 12345L, false, true, 0, false, null),
            Arguments.of("正常启动-无附件", 12345L, false, false, 0, false, null),
            Arguments.of("重试场景", 12345L, true, true, 0, false, null),
            Arguments.of("任务来源1", 12345L, false, true, 1, false, null),
            Arguments.of("任务来源2", 12345L, false, true, 2, false, null)
        );
    }

}
