package com.ideal.script.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.boot.autoconfigure.task.TaskExecutionProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurerSupport;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import javax.annotation.Nonnull;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;


/**
 * 开启spring async和Scheduled 功能
 *
 * <AUTHOR>
 **/
@Configuration
@EnableAsync
@EnableScheduling
public class AsyncConfiguration extends AsyncConfigurerSupport {

    private static final Logger log = LoggerFactory.getLogger(AsyncConfiguration.class);

    private final TaskExecutionProperties taskExecutionProperties;
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    // 默认最小值配置
    private static final int MIN_CORE_POOL_SIZE = 20;
    private static final int MIN_MAX_POOL_SIZE = 200;
    private static final int MIN_QUEUE_CAPACITY = 5000;

    public AsyncConfiguration( TaskExecutionProperties taskExecutionProperties) {
        this.taskExecutionProperties = taskExecutionProperties;
    }
    /**
     * 监控打印async线程池使用情况，1分钟打印一次
     */
    @Scheduled(cron = "0 */1 * * * ?")
    public void ThreadPoolPrint() {
        StringBuilder msg = new StringBuilder();
        ThreadPoolExecutor threadPoolExecutor = threadPoolTaskExecutor.getThreadPoolExecutor();
        msg.append(" max:").append(threadPoolExecutor.getMaximumPoolSize())
           .append(", core:").append(threadPoolExecutor.getCorePoolSize())
           .append(", currentThread:").append(threadPoolExecutor.getPoolSize())
           .append(", largest:").append(threadPoolExecutor.getLargestPoolSize())
           .append(", active:").append(threadPoolExecutor.getActiveCount())
           .append(", queue:").append(threadPoolExecutor.getQueue().size())
           .append(", taskCount:").append(threadPoolExecutor.getTaskCount())
           .append(", completedTaskCount:").append(threadPoolExecutor.getCompletedTaskCount());
        log.info("spring async threadPoolExecutor status:{}", msg);
    }
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return (ex, method, params) -> log.error("catch exception by spring async executing async method '{}'", method, ex);
    }
    /**
     * Async注解异步调用使用的线程池
     * @return Executor
     */
    @Override
    @Nonnull
    @Bean
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        //核心线程池大小
        executor.setCorePoolSize(Math.max(taskExecutionProperties.getPool().getCoreSize(), MIN_CORE_POOL_SIZE));
        //最大线程数
        executor.setMaxPoolSize(Math.max(taskExecutionProperties.getPool().getMaxSize(), MIN_MAX_POOL_SIZE));
        //队列容量
        executor.setQueueCapacity(Math.max(taskExecutionProperties.getPool().getQueueCapacity(), MIN_QUEUE_CAPACITY));
        //空闲活跃时间
        executor.setKeepAliveSeconds((int) Math.max(taskExecutionProperties.getPool().getKeepAlive().getSeconds(), 300));
        //是否允许核心线程超时
        executor.setAllowCoreThreadTimeOut(taskExecutionProperties.getPool().isAllowCoreThreadTimeout());
        //优雅停机，等待线程池任务都执行完毕再销毁
        executor.setWaitForTasksToCompleteOnShutdown(true);
        //线程池销毁等待时间
        executor.setAwaitTerminationSeconds(60);
        //线程名字前缀
        executor.setThreadNamePrefix("async-"+taskExecutionProperties.getThreadNamePrefix());
        //记录错误日志，不抛出异常
        executor.setRejectedExecutionHandler((r, executor1) -> log.error("Async Task rejected - Spring Thread pool is full! " +
                        "Pool size: {}, Active count: {}, Queue size: {}, Task count: {}, " +
                        "Completed task count: {}, Task class: {}",
                executor1.getPoolSize(),
                executor1.getActiveCount(),
                executor1.getQueue().size(),
                executor1.getTaskCount(),
                executor1.getCompletedTaskCount(),
                r.getClass().getSimpleName()));
        executor.initialize();
        threadPoolTaskExecutor = executor;
        return executor;
    }
}
